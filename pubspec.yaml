name: new_neo_invest
description: NEO Invest
version: 1.0.0+21
publish_to: none

environment:
  sdk: ^3.5.0

dependencies:
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.6
  vp_auth:
    path: features/vp_auth
  vp_price_board:
    path: features/vp_price_board
  vp_trading:
    path: features/vp_trading
  vp_loyalty:
    path: features/vp_loyalty
  vp_assets:
    path: features/vp_assets
  vp_settings:
    path: features/vp_settings
  vp_utility:
    path: features/vp_utility
  vp_stock_common:
    path: library/vp_stock_common
  vp_design_system:
    path: library/vp_design_system
  stock_detail:
    path: features/vp_stock_detail
  vp_money:
    path: features/vp_money
  vp_centralize:
    path: features/vp_centralize
  vp_portfolio:
    path: features/vp_portfolio
  vp_finvest:
    path: features/vp_finvest
  vp_notification:
    path: features/vp_notification
  vp_fund:
    path: features/vp_fund
  tuple:
  vp_partner_connection:
    path: features/vp_partner_connection
  vp_ipo:
    path: features/vp_ipo
  app_tracking_transparency:
  go_router:
  equatable:
  flutter_svg:
  shimmer:
  image_picker:
  flutter_sticky_header:
  recaptcha_enterprise_flutter:
  syncfusion_flutter_charts:
  printing:
  vp_core:
    path: library/vp_core
  vp_common:
    path: library/vp_common
  dio:
  
dev_dependencies:
  bloc_test: ^9.1.7
  build_runner:
  flutter_test:
    sdk: flutter
  mocktail: ^1.0.4
  very_good_analysis: ^6.0.0
  custom_lint:
  json_serializable:
  retrofit_generator:
  flutter_gen_runner:
  intl_utils:
## Install derry from https://pub.dev/packages/derry
scripts: derry.yaml
dependency_overrides:
  image_gallery_saver:
    git:
      url: https://github.com/knottx/image_gallery_saver.git
      ref: knottx-latest
  flutter_plugin_android_lifecycle: 2.0.27
  collection: 1.19.1
  intl: ^0.19.0
  value_layout_builder: 0.4.0
  flutter_secure_storage: 9.2.4
flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/icons/mini_app/
flutter_intl:
  enabled: true
  main_locale: vi
  class_name: VPNeoLocalize
  