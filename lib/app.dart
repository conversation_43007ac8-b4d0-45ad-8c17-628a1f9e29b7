import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_biometric_change_detector/flutter_biometric_change_detector.dart';
import 'package:new_neo_invest/core/utils/firebase/firebase_messaging.dart';
import 'package:new_neo_invest/gen/assets.gen.dart';
import 'package:new_neo_invest/generated/l10n.dart';
import 'package:new_neo_invest/main_app_module.dart';
import 'package:vp_auth/gen/assets.gen.dart' as assets;
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_common/vp_common.dart' hide Assets;
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/screens/watchlist/watchlist_bloc.dart';
import 'package:flutter_biometric_change_detector/status_enum.dart';

class RouteTracker {
  static String lastRoute = MainRouter.splash.routeName;
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  final firebaseFcmManager = GetIt.instance.get<FirebaseMessagingManager>();
  late StreamSubscription notiOnClickStreamSubscription;

  late StreamSubscription notiForegroundStreamSubscription;

  @override
  void initState() {
    super.initState();
    ModuleManagement()
        .getModules()
        .forEach((module) => routes.addAll(module.router()));

    expectionWarning();
    // firebaseFcmManager
    //   ..requestPermission()
    //   ..onGeneralFcmToken()
    //   ..setFcmTokenListener(this);

    // notiOnClickStreamSubscription =
    //     firebaseFcmManager.notificationOnClick.listen(_handleNotiOnClick);

    // notiForegroundStreamSubscription = firebaseFcmManager.notificationForeground
    //     .listen(_handleNotiOnForeground);

    GetIt.instance<Alice>()
        .setNavigatorKey(GetIt.instance<NavigationService>().navigatorKey);
  }

  checkBiometric() async {
  

  }

  // @override
  // void onTokenChanged(String? token) {
  //   if (token.hasData) _bloc.registerFcmToken(token!);
  // }

  // @override
  // void onTokenRefresh(String? token) {
  //   if (token != null) {
  //     Appsflyer().appsflyerSdk?.updateServerUninstallToken(token);
  //   }
  // }

  // void _handleNotiOnForeground(FcmNotification notification) async {
  //   if (_bloc.state.totalUnRead == 0) {
  //     await Future.delayed(const Duration(seconds: 1));

  //     _bloc.getTotalUnReadNotification();
  //   }
  // }

  // void _handleNotiOnClick(FcmNotification notification) {
  //   if (mounted) {
  //     _bloc.getTotalUnReadNotification();

  //     Navigator.of(context).pushIfNotExist(NotificationsRouter.notifications);
  //   }
  // }

  void expectionWarning() {
    GetIt.instance<AuthCubit>().expireStream.listen(
      (data) async {
        if (data == DialogType.expire) {
          await VPPopup.oneButton(
            title: isLoggedIn
                ? S.current.account_sessionExpire
                : S.current.account_sessionRegisterExprie,
            content: S.current.account_requestReLogin,
          )
              .copyWith(
                icon: SvgPicture.asset(
                  assets.Assets.icons.icSessionExpire,
                  package: 'vp_auth',
                  width: 62.5,
                  height: 57.5,
                  fit: BoxFit.fitWidth,
                ),
              )
              .copyWith(
                button: VpsButton.primarySmall(
                  title: S.current.account_agree,
                  onPressed: () {
                    GetIt.instance<AuthCubit>().isShowingExpireDialog = false;
                    GetIt.instance<AuthCubit>().logout();
                    GetIt.instance<NavigationService>()
                        .navigatorKey
                        .currentContext
                        ?.go('/noauth-priceBoard');
                  },
                ),
              )
              .showDialog(GetIt.instance<NavigationService>()
                  .navigatorKey
                  .currentContext!);
        }
        if (data == DialogType.maintenance) {
          await showMaintenanceDialog();
        }
      },
    );
  }

  Future<void> showMaintenanceDialog({String? content}) async {
    await VPPopup.oneButton(
      title: VPNeoLocalize.current.systemMaintenanceTitle,
      content: content ?? VPNeoLocalize.current.systemMaintenanceContent,
    )
        .copyWith(
          icon: SvgPicture.asset(
            Assets.icons.icSystemMaintenance,
            package: 'vp_auth',
            width: 62.5,
            height: 57.5,
            fit: BoxFit.fitWidth,
          ),
        )
        .copyWith(
          button: VpsButton.primarySmall(
            title: S.current.account_agree,
            onPressed: () {
              GetIt.instance<AuthCubit>().isShowingExpireDialog = false;
              GetIt.instance<AuthCubit>().logout();
              GetIt.instance<NavigationService>()
                  .navigatorKey
                  .currentContext
                  ?.go(MainRouter.welcome.routeName);
            },
          ),
        )
        .showDialog(
            GetIt.instance<NavigationService>().navigatorKey.currentContext!);
  }

  final List<RouteBase> routes = [];

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: GetIt.instance<ThemeCubit>()),
        BlocProvider.value(value: GetIt.instance<AuthCubit>()),
        BlocProvider(create: (_) => WatchlistBloc()),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<AuthCubit, AuthState>(
            listenWhen: (preState, state) => preState.status != state.status,
            listener: (context, state) {
              if (state.status == AuthStatus.nologin) {
                context.read<WatchlistBloc>().clear();
              }
            },
          ),
        ],
        child: BlocBuilder<ThemeCubit, ThemeState>(
          builder: (BuildContext ctx, state) {
            return BlocBuilder<AuthCubit, AuthState>(
              builder: (BuildContext ctx, state) {
                return LogApiConfigScreen(
                  navigatorKey:
                      GetIt.instance<NavigationService>().navigatorKey,
                  child: MaterialApp.router(
                    title: 'VPBANKS',
                    color: Colors.white,
                    theme: ctx.read<ThemeCubit>().themeData,
                    debugShowCheckedModeBanner: false,
                    locale: const Locale('vi', ''),
                    supportedLocales: const [
                      Locale('en', ''),
                      Locale('vi', ''),
                    ],
                    localizationsDelegates:
                        ModuleManagement().localizationsDelegates(),
                    builder: vpToastBuilder(),
                    routerConfig: router,
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}

final router = GoRouter(
  navigatorKey: GetIt.instance<NavigationService>().navigatorKey,
  refreshListenable: GoRouterRefreshStream(GetIt.instance<AuthCubit>().stream),
  debugLogDiagnostics: true,
  redirect: (context, state) async {
    final path = state.uri.path;
    RouteTracker.lastRoute = path;

    if (path == '/') {
      return MainRouter.splash.routeName;
    }

    if (!GetIt.instance.get<AuthCubit>().isLogined &&
        !path.contains('noauth') &&
        !path.contains('signIn')) {
      final from = state.uri.toString();
      final returnTo = Uri.encodeComponent(from);
      return '/signIn?returnTo=$returnTo';
    }
    // // If logged in and trying to access /login -> send to returnTo or home
    // if (loggedIn && loggingIn) {
    //   final returnTo = state.uri.queryParameters['returnTo'];
    //   if (returnTo != null && returnTo.isNotEmpty) {
    //     return Uri.decodeComponent(returnTo);
    //   }
    //   return '/';
    // }

    return state.uri.toString();
  },
  initialLocation: RouteTracker.lastRoute,
  routes: ModuleManagement().onGenerateRoute(),
);

class GoRouterRefreshStream extends ChangeNotifier {
  GoRouterRefreshStream(Stream<dynamic> stream) {
    notifyListeners();
    _subscription =
        stream.asBroadcastStream().listen((dynamic _) => notifyListeners());
  }
  late final StreamSubscription<dynamic> _subscription;

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}
