import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:recaptcha_enterprise_flutter/recaptcha_enterprise.dart';
import 'package:vp_auth/cubit/sign_in/user_info_mixin.dart';
import 'package:vp_centralize/screen/smart_otp/settings_smart_otp/smart_otp_register_helper/encrypt/rsa_encrypt_helper.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/core/repository/stock_common_repository.dart';

part 'splash_state.dart';

class SplashCubit extends Cubit<SplashState> with UserInfoMixin {
  SplashCubit() : super(SplashInit()) {
    init();
  }

  final int _kAnimationDelayMilli = 1000;

  final LocalAuthentication auth = LocalAuthentication();

  late RemoteConfigService remoteConfig = RemoteConfigService();
  late RealtimeDatabaseService realtimeDatabase = RealtimeDatabaseService();

  final stockCommonRepository = GetIt.instance<StockCommonRepository>();

  int get now => DateTime.now().millisecondsSinceEpoch;

  Future<void> init() async {
 
    final isUpdate = await _checkAppVersion();

    await _initRecaptchaEnterpriseClient();

    //   if (isUpdate && isProduct) return emit(UpdateVersion());

    final startTime = now;

    RsaEncryptHelper().init();

   await getAllStock();

    if (GetIt.instance<AuthCubit>().hasAccessToken()) {
      await getUserInfo(
        onSuccess: (userInfo) {
          emit(NavigateToHomePageState(userInfo: userInfo));
        },
        onUpdatePinForFirst: (_, updatePassword, updatePin) {
          emit(NavigateToUpdatePinState(updatePassword, updatePin));
        },
        onFail: (e) {
          _handleWhenLoginFail(startTime);
        },
      );
      await GetIt.instance<AuthCubit>().setDeviceName();
    } else {
      await _delayEmitEvent(startTime);
    }
  }

  Future<void> getAllStock() async {
    try {
      await stockCommonRepository.getAllStock(loadCache: false);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  void _handleWhenLoginFail(int startTime) {
    // AppData().clear();
    _delayEmitEvent(startTime);
  }

  Future<void> _delayEmitEvent(int startTime) async {
    final remainTime = max(0, _kAnimationDelayMilli - (now - startTime));

    await Future.delayed(Duration(milliseconds: remainTime));

    emit(NavigateToWelcomePageState());
  }

  Future<void> _initRecaptchaEnterpriseClient() async {
    final siteKey = Platform.isAndroid
        ? AppConstants.captchaEnterpriseAndroidKey
        : AppConstants.captchaEnterpriseIosKey;

    try {
      final result =
          await RecaptchaEnterprise.initClient(siteKey, timeout: 10000);
      dlog(result);
    } on PlatformException catch (err) {
      showMessage('Code: ${err.code} Message ${err.message}');
    } catch (err) {
      showMessage(err.toString());
    }
  }

  Future<bool> _checkAppVersion() async {
    try {
      ///get info config
      await remoteConfig.initConfig();
      return false;
      //   return await remoteConfig.needUpdateVersion();
    } catch (e) {
      return false;
    }
  }
}
