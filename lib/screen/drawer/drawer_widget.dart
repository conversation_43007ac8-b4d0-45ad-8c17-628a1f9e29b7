// import 'package:app/common/assets/common_key_assets.dart';
// import 'package:app/common/error/show_error.dart';
// import 'package:app/common/extensions/string_extensions.dart';
// import 'package:app/common/utils/app_launching_utils.dart';
// import 'package:app/common/utils/app_snackbar_utils.dart';
// import 'package:app/common/view/overlay_dialog/dialog_helper.dart';
// import 'package:app/common/view/overlay_dialog/dialog_widget.dart';
// import 'package:app/common/widgets/app_bottom_sheet.dart';
// import 'package:app/common/widgets/app_icon_bg.dart';
// import 'package:app/common/widgets/divider_widget.dart';
// import 'package:app/common/widgets/vpbank_loading.dart';
// import 'package:app/global/analytics/app_tracking.dart';
// import 'package:app/global/analytics/app_tracking_event.dart';
// import 'package:app/global/build_config/stock_build_config.dart';
// import 'package:app/global/firebase/realtime_database/realtime_database_service.dart';
// import 'package:app/global/firebase/remote_config/remote_config_service.dart';
// import 'package:app/packages/bond_service/common/constains.dart';
// import 'package:app/packages/centralize_OTP/localization/localize_key.dart';
// import 'package:app/packages/centralize_OTP/model/params/get_smart_otp_params.dart';
// import 'package:app/packages/centralize_OTP/smart_otp/get_smart_otp/get_smart_otp.dart';
// import 'package:app/packages/centralize_OTP/smart_otp/settings_smart_otp/smart_otp_register_helper/smart_otp_helper.dart';
// import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
// import 'package:app/packages/shared/modules/auth/presentation/auth_router.dart';
// import 'package:app/packages/shared/modules/customer_care/router/customer_care_router.dart';
// import 'package:app/packages/shared/modules/game_birthday_2025/platform_game_birthday_2025.dart';
// import 'package:app/packages/shared/modules/home/<USER>/repo/home_repository.dart';
// import 'package:app/packages/shared/modules/home/<USER>/assets/res.dart';
// import 'package:app/packages/shared/modules/home/<USER>/feature/bloc/home_bloc.dart';
// import 'package:app/packages/shared/modules/home/<USER>/feature/drawer/user_info_widget.dart';
// import 'package:app/packages/shared/modules/home/<USER>/lang/key_lang.dart';
// import 'package:app/packages/shared/modules/home/<USER>/lang/localized_value.dart';
// import 'package:app/packages/shared/modules/home/<USER>/router/home_router.dart';
// import 'package:app/packages/shared/modules/home/<USER>/widget/home_order_confirm/bloc/home_order_confirm_cubit.dart';
// import 'package:app/packages/shared/modules/money/money_presentation/feature/money_cash_in/view/components/vpbank_loading_money.dart';
// import 'package:app/packages/shared/modules/partner_connection/presentation/router/partner_connection_router.dart';
// import 'package:app/packages/shared/modules/settings/router/setting_router.dart';
// import 'package:app/packages/stock/modules/place_order/presentation/feature/place_order/bloc/place_order_bloc_map_import.dart';
// import 'package:core_ui/widgets/design_system/button/button_widget.dart';
// import 'package:core_ui/widgets/design_system/dialog/notifty_dialog.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
// import 'package:tuple/tuple.dart';
// import 'package:url_launcher/url_launcher.dart';

// void showUpdateContractDialog({VoidCallback? callback}) async {
//   final context = GetIt.instance<NavigationService>().navigatorKey.currentContext;

//   if (context == null) return;

//   DialogHelper().show(
//     context,
//     DialogWidget.custom(
//       closable: false,
//       child: BaseDialog(
//         barrierDismissible: false,
//         title: getHomeLang(HomeLangKey.contractOTPDialogTitle),
//         textButtonRight: getHomeLang(HomeLangKey.contractOTPConfirmButton),
//         colorButtonRight: ColorUtils.primary,
//         colorBorderButtonLeft: ColorUtils.gray700,
//         image: CommonKeyAssets.icUpdateContract,
//         imagePadding: const EdgeInsets.only(bottom: 16),
//         iconSize: 96,
//         contentWidget: HtmlWidget(
//           getHomeLang(HomeLangKey.contractOTPDialogContent),
//           textStyle: vpTextStyle.body14?,
//         ),
//         onPressedRight: () {
//           DialogHelper().hide(context);

//           callback?.call();
//         },
//       ),
//     ),
//   );
// }

// void showUnRegisterSmartOTPDialogType1({
//   bool registerInOtherDevice = false,
//   String? deviceRegistered,
// }) {
//   final context = GetIt.instance<NavigationService>().navigatorKey.currentContext;

//   if (context == null) return;

//   DialogHelper().show(
//     context,
//     DialogWidget.custom(
//       child: BaseDialog(
//         barrierDismissible: true,
//         title: LocalizeKey.unregisterSmartOTPTitleType1,
//         content: LocalizeKey.unregisterSmartOTPContentType1,
//         textButtonRight: LocalizeKey.unregisterSmartOTPButtonRegisterType1,
//         colorButtonRight: ColorUtils.primary,
//         colorBorderButtonLeft: ColorUtils.gray700,
//         image: CommonKeyAssets.icCancel,
//         imagePadding: const EdgeInsets.only(bottom: 24),
//         iconSize: 80,
//         showCloseIcon: true,
//         onPressedIconClose: () {
//           DialogHelper().hide(context);
//         },
//         onPressedRight: () {
//           DialogHelper().hide(context);

//           navigation.navigateTo(
//             SettingRouter.registerSmartOTPSplash,
//             arguments: Tuple2(registerInOtherDevice, deviceRegistered),
//           );
//         },
//       ),
//     ),
//   );
// }

import 'package:flutter/material.dart';
import 'package:new_neo_invest/cubit/home/<USER>';
import 'package:new_neo_invest/gen/assets.gen.dart';
import 'package:new_neo_invest/generated/l10n.dart';
import 'package:new_neo_invest/screen/drawer/widget/drawer_item_widget.dart';
import 'package:new_neo_invest/screen/drawer/widget/user_info_widget.dart';
import 'package:tuple/tuple.dart';
import 'package:vp_centralize/vp_centralize.dart';
import 'package:vp_common/vp_common.dart' hide Assets;
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_loyalty/router/loyalty_router.dart';
import 'package:vp_loyalty/vp_loyalty.dart';
import 'package:vp_partner_connection/vp_partner_connection.dart';
import 'package:vp_settings/vp_settings.dart';

class DrawerWidget extends StatefulWidget {
  const DrawerWidget({
    super.key,
    required this.confirmLogout,
    required this.homeCubit,
    this.onToggleDrawer,
    // required this.homeOrderConfirmCubit,
  });

  final Function confirmLogout;

  final HomeCubit homeCubit;

  final VoidCallback? onToggleDrawer;

  //final HomeOrderConfirmCubit homeOrderConfirmCubit;

  @override
  State<DrawerWidget> createState() => _DrawerWidgetState();
}

class _DrawerWidgetState extends State<DrawerWidget> {
  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topRight: Radius.circular(12),
        bottomRight: Radius.circular(12),
      ),
      child: Drawer(
        child: ColoredBox(
          color: vpColor.backgroundElevation0,
          child: SafeArea(
            child: Column(
              children: [
                const SizedBox(height: 16),
                InkWell(
                    child: UserInfoWidget(homeCubit: widget.homeCubit),
                    onTap: () {
                      widget.onToggleDrawer?.call();
                      // widget.homeBloc.checkLinkWithLynkId();
                    }),
                const SizedBox(height: 16),
                const DividerWidget(),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    children: [
                      DrawerItemWidget(
                        title: VPNeoLocalize.current.home_info_customer,
                        desc: VPNeoLocalize.current.home_desc_customer,
                        icon: Assets.icons.icUser,
                        onTap: () {
                          widget.onToggleDrawer?.call();
                          context.push(SettingRouter.customerInfo.routeName);
                        },
                      ),
                      DrawerItemWidget(
                        title: VPNeoLocalize.current.home_membershipOffer,
                        desc: VPNeoLocalize.current.home_descMember,
                        icon: Assets.icons.icMemberRank,
                        onTap: () async {
                          final isValidLoyaltyAccount = await context
                              .read<CheckLoyaltyAccountCubit>()
                              .checkValidLoyaltyAccount();
                          if (isValidLoyaltyAccount) {
                            widget.onToggleDrawer?.call();
                            widget.homeCubit.cachedImageForPreLoaded(context);
                            context.push(LoyaltyRouter.loyalty.routeName);
                          }
                        },
                      ),
                      DrawerItemWidget(
                        title: VPNeoLocalize.current.home_partnerConnection,
                        icon: Assets.icons.icPartnerConnection,
                        onTap: () {
                          widget.onToggleDrawer?.call();
                          context.push(PartnerConnectionRouter
                              .openPartnerList.routeName);
                        },
                      ),
                      DrawerItemWidget(
                        title: VPNeoLocalize.current.home_security,
                        icon: Assets.icons.icSecurity,
                        onTap: () {
                          widget.onToggleDrawer?.call();
                          context.push(SettingRouter.settingSecurity.routeName);
                        },
                      ),
                      DrawerItemWidget(
                        title: VPNeoLocalize.current.home_setup,
                        icon: Assets.icons.icSetup,
                        onTap: () {
                          widget.onToggleDrawer?.call();
                          context.push(SettingRouter.settingSetup.routeName);
                        },
                      ),
                      DrawerItemWidget(
                        title: VPNeoLocalize.current.home_support,
                        icon: Assets.icons.icSupport,
                        onTap: () {
                          widget.onToggleDrawer?.call();
                          context.push(SettingRouter.settingSupport.routeName);
                        },
                      ),
                      const SizedBox(height: 10),
                      const DividerWidget(),
                      const SizedBox(height: 4),
                      DrawerItemWidget(
                        title: VPNeoLocalize.current.home_introduceFriend,
                        desc: VPNeoLocalize
                            .current.home_introduceFriendAndCustomer,
                        icon: Assets.icons.icAddUser,
                        onTap: () {
                          widget.onToggleDrawer?.call();
                          context
                              .push(LoyaltyRouter.shareReferralPage.routeName);
                        },
                      ),
                      const SizedBox(height: 10),
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                VpsButton.teriatySmall(
                  onPressed: () {
                    widget.onToggleDrawer?.call();
                    checkSmartOTP();
                  },
                  title: VPNeoLocalize.current.home_get_smart_oTP,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  width: double.infinity,
                  child: VpsButton.secondarySmall(
                    title: VPNeoLocalize.current.home_logout,
                    onPressed: () {
                      widget.onToggleDrawer?.call();
                      widget.confirmLogout.call();
                    },
                  ),
                ),
                Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Text(
                      '${VPNeoLocalize.current.home_version}${widget.homeCubit.verison}',
                      style: vpTextStyle.captionRegular
                          .copyColor(themeData.disabledColor),
                    ))
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> checkSmartOTP() async {
    try {
      showDialogLoading();

      final result =
          await SmartOTPHelper().getSmartOTPStatus(throwWhenError: true);

      final isRegistered = result?.item1 == SmartOTPStatus.registerInThisDevice;

      hideDialogLoading();

      if (isRegistered) {
        getContext.push(
          CentralizeRouter.smartOTP.routeName,
          extra: GetSmartOTPParams(showButtonForgotPin: true),
        );
      } else {
        checkShowPopupSmartOTP(result!);
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      hideDialogLoading();
      showError(e);
    }
  }

  void checkShowPopupSmartOTP(Tuple2<SmartOTPStatus, String?> tuple2) {
    final isRegistered = tuple2.item1 == SmartOTPStatus.registerInOtherDevice;

    showRegisterSmartOTPDialog(
      context: getContext,
      registerInOtherDevice: isRegistered,
      deviceRegistered: tuple2.item2,
    );
  }
}
