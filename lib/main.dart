import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:new_neo_invest/app.dart';
import 'package:new_neo_invest/main_app_module.dart';
import 'package:stock_detail/stock_detail_module.dart';
import 'package:vp_assets/vp_assets.dart';
import 'package:vp_auth/vp_auth_module.dart';
import 'package:vp_centralize/vp_centralize.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_finvest/vp_finvest.dart';
import 'package:vp_fund/vp_fund.dart';
import 'package:vp_ipo/vp_ipo.dart';
import 'package:vp_loyalty/vp_loyalty.dart';
import 'package:vp_money/vp_money_module.dart';
import 'package:vp_notification/vp_notification_module.dart';
import 'package:vp_partner_connection/vp_partner_connection_module.dart';
import 'package:vp_portfolio/vp_portfolio.dart';
import 'package:vp_price_board/vp_price_board_module.dart';
import 'package:vp_settings/vp_settings_module.dart';
import 'package:vp_stock_common/screens/watchlist/watchlist_bloc.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/vp_trading.dart';
import 'package:vp_utility/vp_utility.dart';

void main() async {
  F.appFlavor = Flavor.uat;
  WidgetsFlutterBinding.ensureInitialized();

  final modules = <Module>[
    VpStockCommonModule(),
    MainAppModule(),
    AuthModule(),
    VpPriceBoardModule(),
    AssetsModule(),
    TradingModule(),
    LoyaltyModule(),
    VPStockDetailModule(),
    VpUtilityModule(),
    VpMoneyModule(),
    SettingsModule(),
    VpCentralizeModule(),
    VpPortfolioModule(),
    VpFinvestModule(),
    PartnerConnectionModule(),
    VPNotificationModule(),
    VpFundModule(),
    IpoModule(),
  ];

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  ModuleManagement().addModules(modules);
  await ModuleManagement().injectDependencies();

  runApp(const MyApp());
}
