import 'package:vp_socket/vp_socket.dart';

mixin FuTop10SocketMixin {
  ListenerHandle? listenerHandle;

  VPInvestSub? topic;

  final socket = SocketFactory.get(SocketType.stock) as IsolateInvestSocket;

  void subscribe(Set<String>? symbols) {
    if (symbols == null || symbols.isEmpty) return;

    topic = VPInvestSub(
      symbols: symbols,
      channel: VPSocketChannel.fuTopNPrice.name,
    );

    socket.subscribe(topic!);

    listenerHandle = socket.addListener(
      topic!,
      listener: _listener,
      selector:
          (_, data) => data is VPFuTopNData && symbols.contains(data.symbol),
    );
  }

  void unsubscribe() {
    listenerHandle?.dispose();
  }

  void _listener(VPSocketData? data) {
    if (data is VPFuTopNData) {
      listener(data);
    }
  }

  void listener(VPFuTopNData? data) {}
}
