import 'package:vp_socket/vp_socket.dart';

mixin StockInfoSocketMixin {
  VPInvestSub? topic;

  ListenerHandle? listenerHandle;

  final socket = SocketFactory.get(SocketType.stock) as IsolateInvestSocket;

  void subscribeStockInfo(Set<String>? symbols) {
    if (symbols == null || symbols.isEmpty) return;

    listenerHandle?.dispose();

    topic = VPInvestSub(
      symbols: symbols,
      channel: VPSocketChannel.stockInfo.name,
    );

    socket.subscribe(topic!);

    listenerHandle = socket.addListener(
      topic!,
      listener: _onSocketStockInfoListener,
      selector:
          (_, data) => data is VPStockInfoData && symbols.contains(data.symbol),
    );
  }

  void unsubscribeStockInfo() {
    listenerHandle?.dispose();
    listenerHandle = null;
  }

  void _onSocketStockInfoListener(VPSocketData? data) {
    if (data is VPStockInfoData) {
      onSocketStockInfoListener(data);
    }
  }

  void onSocketStockInfoListener(VPStockInfoData data) {}
}
