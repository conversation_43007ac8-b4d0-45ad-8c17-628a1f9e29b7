import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class VPStatusBuilder extends StatelessWidget {
  const VPStatusBuilder({
    required this.builder,
    required this.apiStatus,
    this.shinkWrap = false,
    this.loadingBuilder,
    this.errorBuilder,
    this.height,
    this.child,
    super.key,
  });

  final ApiStatus apiStatus;

  final Widget? child;

  final bool shinkWrap;

  final double? height;

  final Widget Function(BuildContext context)? errorBuilder;

  final Widget Function(BuildContext context)? loadingBuilder;

  final Widget Function(BuildContext context, Widget? child) builder;

  @override
  Widget build(BuildContext context) {
    if (apiStatus.isLoading) {
      return loadingBuilder?.call(context) ??
          (shinkWrap
              ? const VPInnerLoading()
              : SizedBox(
                height: height,
                child: const Center(child: VPInnerLoading()),
              ));
    }

    if (apiStatus.isError) {
      return errorBuilder?.call(context) ??
          SizedBox(height: height, child: const VPErrorView());
    }

    if (apiStatus.isDone) {
      return builder(context, child);
    }

    return const SizedBox.shrink();
  }
}
