import 'package:flutter/material.dart';
import 'package:vp_socket/vp_socket.dart';

class VPSocketInvestmentBuilder<T extends VPSocketInvestData>
    extends StatefulWidget {
  const VPSocketInvestmentBuilder({
    required this.symbol,
    required this.channel,
    required this.builder,
    this.child,
    this.buildWhen,
    super.key,
  });

  final String? symbol;

  final String channel;

  final Widget? child;

  final bool Function(T? preData, T? data)? buildWhen;

  final Widget Function(
    BuildContext context,
    T? preData,
    T? data,
    Widget? child,
  )
  builder;

  @override
  State<VPSocketInvestmentBuilder<T>> createState() =>
      _VPSocketSelectorState<T>();
}

class _VPSocketSelectorState<T extends VPSocketInvestData>
    extends State<VPSocketInvestmentBuilder<T>> {
  final socket = SocketFactory.get(SocketType.stock) as IsolateInvestSocket;

  ListenerHandle? listenerHandle;

  T? preData;
  T? data;

  VPInvestSub? topic;

  @override
  void didUpdateWidget(covariant VPSocketInvestmentBuilder<T> oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.symbol != widget.symbol ||
        oldWidget.channel != widget.channel) {
      removeListener();

      subscribeTopic();

      data = null;
    }
  }

  void listener(VPSocketData? newData) {
    if (newData is! T || !mounted) return;

    if (widget.buildWhen == null ||
        widget.buildWhen!.call(data, newData) == true) {
      preData = data;
      setState(() => data = newData);
    }
  }

  void subscribeTopic() {
    if (widget.symbol == null) return;

    topic = VPInvestSub(symbols: {widget.symbol!}, channel: widget.channel);

    socket.subscribe(topic!);

    listenerHandle = socket.addListener(
      topic!,
      listener: listener,
      selector:
          (_, data) =>
              data is T &&
              data.symbol == topic!.symbols.first &&
              data.channel == topic!.channel,
    );
  }

  @override
  void initState() {
    super.initState();

    subscribeTopic();
  }

  @override
  void dispose() {
    removeListener();
    super.dispose();
  }

  void removeListener() {
    listenerHandle?.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.builder(context, preData, data, widget.child);
  }
}
