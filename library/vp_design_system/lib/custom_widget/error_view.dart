import 'package:flutter/material.dart';
import 'package:vp_design_system/gen/assets.gen.dart';
import 'package:vp_design_system/themes/utils.dart';
import 'package:vp_design_system/vp_design_system.dart';

class VPErrorView extends StatelessWidget {
  const VPErrorView({
    this.content,
    this.onRetry,
    this.alignment,
    this.margin = const EdgeInsets.only(top: 40),
    super.key,
  });

  final String? content;

  final EdgeInsets margin;

  final VoidCallback? onRetry;

  final Alignment? alignment;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: margin,
      child: Align(
        alignment: alignment ?? Alignment.topCenter,
        child: Column(
          spacing: 16,
          mainAxisSize: MainAxisSize.min,
          children: [
            Assets.icons.noData.icNodata.svg(width: 120, height: 120),
            Text(
              content ?? 'Đã có lỗi xảy ra',
              style: context.textStyle.body14
                  ?.copyWith(color: context.colors.textPrimary),
            ),
            if (onRetry != null)
              VpsButton.teriatySmall(
                title: 'Nhấn để thử lại',
                onPressed: () => onRetry?.call(),
              ),
          ],
        ),
      ),
    );
  }
}
