import 'package:vp_socket/base/lifecycle_connectivity_bridge.dart';
import 'package:vp_socket/base/socket_controller.dart';
import 'package:vp_socket/base/subscription_manager.dart';
import 'package:vp_socket/factory/socket_account_factory.dart';
import 'package:vp_socket/base/base_socket.dart';
import 'package:vp_socket/socket_impl/account/account_codec.dart';
import 'package:vp_socket/socket_impl/account/account_sub.dart';
import 'package:vp_socket/socket_impl/account/account_topic_key.dart';

class VPSocketAccountConnect extends VPBaseSocket
    with
        SubscriptionManager<VPAccountSub, AccountTopicKey>,
        SocketLifecycleConnectivityMixin {
  VPSocketAccountConnect() {
    initSubscriptionManager(codec: VPAccountCodec());
  }

  @override
  String get path => '/trading-stream/realtime';

  @override
  String get baseUrl => 'wss://neopro-uat.vpbanks.com.vn';

  final _controller = VPSocketController(VPAccountCodec());

  SocketLifecycleConnectivityObserver? _bridge;

  @override
  Future<void> connect() {
    _bridge ??= SocketLifecycleConnectivityObserver(this)..start();

    return super.connect();
  }

  @override
  void onConnected() {
    super.onConnected();

    subscribe(VPAccountAuthSub(channel: 'auth', token: token!));
  }

  @override
  void transformData(dynamic rawData) {
    final data = defaultSocketAccountTransformData(rawData);

    _controller.add(data);
  }

  ListenerHandle addListener(
    VPAccountSub sub, {
    required SocketListener listener,
    required SocketSelector selector,
    bool emitImmediately = false,
  }) {
    return _controller.addListener(
      sub,
      listener: listener,
      selector: selector,
      emitImmediately: emitImmediately,
    );
  }

  @override
  Future<void> dispose() {
    _bridge?.stop();
    _controller.dispose();
    return super.dispose();
  }

  @override
  bool get authenticationRequired => true;

  @override
  String? get token => '';

  @override
  bool Function()? get canRetryPredicate => () => isReconnectAllowed;

  @override
  void connectNow() => connect();

  @override
  Future<void> closeNoRetry() => close();
}
