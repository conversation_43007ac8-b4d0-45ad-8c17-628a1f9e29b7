import 'dart:convert';

import 'package:vp_socket/base/subscription.dart';

/// Tiền – chỉ theo channel
sealed class VPAccountSub extends VPSocketSubscription {
  const VPAccountSub({required this.channel});

  final String channel;

  @override
  List<Object?> get props => [channel];
}

class VPAccountAuthSub extends VPAccountSub {
  const VPAccountAuthSub({required super.channel, required this.token});

  final String token;

  @override
  String get subscribeData => jsonEncode({"action": channel, "data": token});

  @override
  String get unsubscribeData => jsonEncode({"action": channel, "data": token});

  @override
  List<Object?> get props => [channel, token];
}
