import 'package:vp_socket/codec/codec.dart';
import 'package:vp_socket/data/socket_data.dart';
import 'package:vp_socket/socket_impl/account/account_sub.dart';
import 'package:vp_socket/socket_impl/account/account_topic_key.dart';

class VPAccountCodec extends SocketCodec<VPAccountSub, AccountTopicKey> {
  @override
  List<AccountTopicKey> toTopicKeys(VPAccountSub s) => switch (s) {
        VPAccountAuthSub(:final channel, :final token) => [
            AuthTopicKey(channel: channel, token: token),
          ],
      };

  @override
  VPAccountSub? fromTopicKeys(List<AccountTopicKey> keys) {
    if (keys.length != 1) return null;
  }

  @override
  List<AccountTopicKey> extractKeys(VPSocketData? d) {
    final ch = d?.channel;
    if (ch == null) return const [];
    return [const AuthTopicKey(channel: '', token: '')];
  }
}
