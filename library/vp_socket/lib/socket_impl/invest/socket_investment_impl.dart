import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:vp_socket/base/schema_manager.dart';
import 'package:vp_socket/base/subscription_manager.dart';
import 'package:vp_socket/data/disconnect_info.dart';
import 'package:vp_socket/base/base_socket.dart';
import 'package:vp_socket/socket_impl/invest/invest_codec.dart';
import 'package:vp_socket/socket_impl/invest/invest_decode_message.dart';
import 'package:vp_socket/socket_impl/invest/invest_sub.dart';
import 'package:vp_socket/socket_impl/invest/invest_topic_key.dart';
import 'package:vp_socket/socket_impl/invest/isolate_invest_socket.dart';

class VPSocketInvestConnect extends VPBaseSocket
    with SubscriptionManager<VPInvestSub, VPInvestTopicKey> {
  VPSocketInvestConnect({required this.onData}) {
    initSubscriptionManager(codec: VPInvestCodec());
  }

  final Function(Map<String, dynamic> data) onData;

  @override
  String get path => '/broker';

  @override
  String get baseUrl => 'wss://stockstream-uat-krx.vpbanks.com.vn';

  Map<String, dynamic>? _schemas;

  bool get isSynced => _schemas != null && _schemas!.isNotEmpty;

  final InvestDecodeMessage _decoder = InvestDecodeMessage();

  PostDecodePolicyResolver? postPolicyResolver;

  void setThrottlePolicyResolver(PostDecodePolicyResolver? postPolicyResolver) {
    this.postPolicyResolver = postPolicyResolver;
  }

  void _onDataDecodedListener(Map<String, dynamic> data) {
    onData.call(data);
  }

  @override
  void onConnected() {
    super.onConnected();

    _decoder.reset();
    _decoder.setListener(_onDataDecodedListener);
    _decoder.setThrottlePolicyResolver(postPolicyResolver);

    _performSchemaSync();
  }

  @override
  void onDisconnect(DisconnectInfo info) {
    super.onDisconnect(info);

    _decoder.reset();
    _schemas = null;
  }

  @override
  Future<void> dispose() async {
    _decoder.close();
    _schemas = null;

    super.close();
  }

  Future<void> _performSchemaSync() async {
    try {
      final syncMessage = await SchemaManager.createSyncMessage();
      final syncJson = jsonEncode(syncMessage);

      send(syncJson);
    } catch (e, stackTrace) {
      debugPrint('❌ Failed to send sync message: $e');
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  void _handleBinaryMessage(Uint8List data) {
    try {
      if (data.length < 2) return;

      final schemaId = data[0];

      if (!isSynced && schemaId == 101) {
        _handleSyncResponse(data);
      } else if (isSynced) {
        _decodeMessage(data);
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      debugPrint('❌ Error handling binary message: $e');
    }
  }

  void _handleSyncResponse(Uint8List data) async {
    try {
      final response = await SchemaManager.handleSyncResponse(data);

      if (response.isMatch) {
        _schemas = response.serverSchemas;
        _decoder.setSchemas(_schemas);
      } else {
        send(jsonEncode({'type': 'sync', 'data': response.newHash}));
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      debugPrint('❌ Error handling sync response: $e');
    }
  }

  void _decodeMessage(dynamic data) {
    _decoder.decodeMessage(data);
  }

  @override
  void transformData(dynamic data) {
    if (data is List<int>) {
      _handleBinaryMessage(Uint8List.fromList(data));
    }
  }

  @override
  bool get authenticationRequired => false;
}
