import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:vp_socket/base/subscription.dart';

part 'invest_sub.g.dart';

/// Bảng giá/<PERSON><PERSON><PERSON> sinh (nhiều symbol trong 1 channel)
@JsonSerializable()
class VPInvestSub extends VPSocketSubscription {
  const VPInvestSub({required this.symbols, required this.channel});

  final Set<String> symbols;

  final String channel;

  String get id => props.join('|');

  @override
  String get subscribeData => jsonEncode(
      {"type": "sub", "channel": channel, "listId": symbols.toList()});

  @override
  String get unsubscribeData => jsonEncode(
      {"type": "unsub", "channel": channel, "listId": symbols.toList()});

  factory VPInvestSub.fromJson(Map<String, dynamic> json) =>
      _$VPInvestSubFromJson(json);

  Map<String, dynamic> toJson() => _$VPInvestSubToJson(this);

  @override
  List<Object?> get props => [symbols, channel];
}
