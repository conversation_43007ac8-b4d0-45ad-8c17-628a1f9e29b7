import 'dart:async';
import 'dart:isolate';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:vp_socket/base/lifecycle_connectivity_bridge.dart';
import 'package:vp_socket/base/socket_controller.dart';
import 'package:vp_socket/data/enum/connection_state.dart';
import 'package:vp_socket/data/method_invocation.dart';
import 'package:vp_socket/data/throttle_policy.dart';
import 'package:vp_socket/data/throttle_rule.dart';
import 'package:vp_socket/extensions/socket_controller_extensions.dart';
import 'package:vp_socket/factory/socket_investment_factory.dart';
import 'package:vp_socket/interceptor/logging_interceptor.dart';
import 'package:vp_socket/socket_impl/invest/invest_codec.dart';
import 'package:vp_socket/socket_impl/invest/invest_sub.dart';
import 'package:vp_socket/socket_impl/invest/invest_topic_key.dart';
import 'package:vp_socket/base/base.dart';
import 'package:vp_socket/socket_impl/invest/socket_investment_impl.dart';

typedef PostDecodePolicyResolver = ThrottlePolicy? Function(dynamic channel);

class IsolateInvestSocket extends BaseSocket
    with SocketLifecycleConnectivityMixin {
  bool _disposed = false;

  Isolate? _isolate;
  SendPort? _sendPort;
  ReceivePort? _receivePort;
  StreamSubscription? _subscription;

  Completer<void>? _ready;

  SocketLifecycleConnectivityObserver? _bridge;

  late final _controller = VPSocketController<VPInvestSub, VPInvestTopicKey>(
    VPInvestCodec(),
    onUnsubscribe: (sub) => unsubscribe(sub),
  );

  static void isolateEntryPoint(Map<String, dynamic> data) {
    final token = data['token'] as RootIsolateToken;
    final mainSendPort = data['sendPort'] as SendPort;

    BackgroundIsolateBinaryMessenger.ensureInitialized(token);

    final isolateReceivePort = ReceivePort();
    mainSendPort.send(isolateReceivePort.sendPort);

    var appInBackground = false;
    var networkAvailable = true;
    final rules = <ThrottleRule>[];

    /// channel: int or string
    ThrottlePolicy? postPolicyResolver(dynamic channel) {
      if (channel == null) {
        return null;
      }

      final keySym = rules.firstWhereOrNull((r) => r.channel == channel);

      if (keySym == null) return null;

      return ThrottlePolicy.of(Duration(milliseconds: keySym.intervalMs));
    }

    final socket = VPSocketInvestConnect(
      onData: (d) => mainSendPort.send(d),
    )
      ..setThrottlePolicyResolver(postPolicyResolver)
      ..addInterceptor(LoggingInterceptor())
      ..canRetryPredicate = () => !appInBackground && networkAvailable;

    Future<void> ensurePolicyConnect() async {
      if (!appInBackground && networkAvailable) {
        socket.connect();
      }
    }

    Future<void> closeNoRetry() async {
      socket.close();
    }

    isolateReceivePort
        .map((message) => MethodInvocation.fromJson(message))
        .listen((inv) {
      switch (inv.method) {
        case 'connect':
          ensurePolicyConnect();
          break;
        case 'reconnect':
          socket.reconnect();
          break;
        case 'subscribe':
          socket.subscribe(VPInvestSub.fromJson(inv.arguments));
          break;
        case 'unsubscribe':
          socket.unsubscribe(VPInvestSub.fromJson(inv.arguments));
          break;
        case 'close':
          socket.close();
          break;
        case 'dispose':
          socket.dispose();
          break;
        case 'appBackground':
          appInBackground = true;
          closeNoRetry();
          break;
        case 'appForeground':
          appInBackground = false;
          ensurePolicyConnect();
          break;
        case 'networkDown':
          networkAvailable = false;
          closeNoRetry();
          break;
        case 'networkUp':
          networkAvailable = true;
          ensurePolicyConnect();
          break;
        case 'setThrottle':
          final List list = (inv.arguments['rules'] as List? ?? const []);
          rules
            ..clear()
            ..addAll(list.map(
                (e) => ThrottleRule.fromJson(Map<String, dynamic>.from(e))));
          break;
        default:
          break;
      }
    });
  }

  Future<void> init() async {
    if (_isolate != null || _disposed) return;

    try {
      _ready ??= Completer<void>();
      _receivePort = ReceivePort();

      _isolate = await Isolate.spawn(isolateEntryPoint, {
        'sendPort': _receivePort!.sendPort,
        'token': RootIsolateToken.instance!,
      });

      _subscription = _receivePort!.listen(
        (message) {
          if (_disposed) return;

          if (message is SendPort) {
            _sendPort = message;

            if (_ready != null && !_ready!.isCompleted) _ready!.complete();

            return;
          }

          if (message is Map) {
            final type = message['type'];

            if (type == 'dataBatch') {
              final list = (message['data']?['list'] as List?) ?? const [];
              _controller.addBatch(list);
            } else {
              _controller.add(defaultInvestmentSocketTransformData(message));
            }
          }
        },
        onError: (e, st) {
          _onTeardown(e, st);
        },
        onDone: () {
          if (_disposed) return;

          _onTeardown('Isolate port closed', StateError('Isolate port closed'));
        },
        cancelOnError: true,
      );
    } catch (e, st) {
      debugPrintStack(stackTrace: st);

      _onTeardown(e, st);
    }
  }

  void _onTeardown(e, st) {
    _subscription?.cancel();
    _subscription = null;
    _receivePort?.close();
    _receivePort = null;
    _sendPort = null;
    _isolate?.kill(priority: Isolate.immediate);
    _isolate = null;
    _ready?.completeError(e, st);
    _ready = null;
    _bridge?.stop();
  }

  Future<void> _sendNow(String method, [Object? args]) async {
    if (_disposed) return;

    await init();

    await _ready?.future;

    _sendPort?.send(MethodInvocation(method: method, arguments: args).toJson());
  }

  @override
  Future<void> connect() async {
    await _sendNow('connect');
    _bridge ??= SocketLifecycleConnectivityObserver(this)..start();
  }

  Future<void> subscribe(VPInvestSub sub) =>
      _sendNow('subscribe', sub.toJson());

  Future<void> unsubscribe(VPInvestSub sub) =>
      _sendNow('unsubscribe', sub.toJson());

  Future<void> setThrottleRules(List<ThrottleRule> rules) async {
    await _sendNow('setThrottle', {
      'rules': rules.map((r) => r.toJson()).toList(),
    });
  }

  @override
  Future<void> appForeground() async => _sendNow('appForeground');

  @override
  Future<void> appBackground() async => _sendNow('appBackground');

  @override
  Future<void> networkUp() async => _sendNow('networkUp');

  @override
  Future<void> networkDown() async => _sendNow('networkDown');

  ListenerHandle addListener(
    VPInvestSub sub, {
    required SocketListener listener,
    required SocketSelector selector,
    bool emitImmediately = false,
  }) {
    return _controller.addListener(
      sub,
      listener: listener,
      selector: selector,
      emitImmediately: emitImmediately,
    );
  }

  void removeListener(VPInvestSub sub, {required SocketListener listener}) {
    _controller.removeListener(sub, listener: listener);
  }

  @override
  Future<void> close() => _sendNow('close');

  @override
  Future<void> dispose() async {
    if (_disposed) return;

    _sendNow('dispose');

    _disposed = true;
    _bridge?.stop();

    _subscription?.cancel();
    _controller.dispose();
    _receivePort?.close();
    _isolate?.kill(priority: Isolate.immediate);

    _subscription = null;
    _receivePort = null;
    _isolate = null;
    _sendPort = null;

    if (_ready != null && !_ready!.isCompleted) {
      _ready!.completeError(StateError('Disposed'));
    }
    _ready = null;
  }

  @override
  Future<void> reconnect() => _sendNow('reconnect');

  @override
  ConnectionStateX get state => throw UnimplementedError();

  @override
  Stream<ConnectionStateX> get stateStream => throw UnimplementedError();
}
