import 'dart:convert';

import 'package:dart_avro/schema/complex_types.dart';
import 'package:dart_avro/utils/data_buffer.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_socket/data/throttle_policy.dart';
import 'package:vp_socket/extensions/binary_delay_throttle.dart';
import 'package:vp_socket/extensions/per_symbol_delay_throttle_2.dart';
import 'package:vp_socket/socket_impl/invest/isolate_invest_socket.dart';

enum BinaryChannel {
  stockInfo(3);

  const BinaryChannel(this.value);

  final num value;
}

class InvestDecodeMessage {
  InvestDecodeMessage({
    this.duration = const Duration(milliseconds: 200),
  });

  final Duration duration;

  Function(Map<String, dynamic>)? _listener;

  Map<String, RecordType>? _schemas;

  PostDecodePolicyResolver? postPolicyResolver;

  void setThrottlePolicyResolver(PostDecodePolicyResolver? postPolicyResolver) {
    this.postPolicyResolver = postPolicyResolver;
  }

  void setSchemas(Map<String, dynamic>? schemas) {
    if (schemas == null || schemas.isEmpty) {
      _schemas = null;
    } else {
      _schemas ??= {};
      _schemas?.clear();

      schemas.forEach((key, value) {
        _schemas![key] = RecordType.fromJson(value);
      });
    }
  }

  void setListener(Function(Map<String, dynamic>)? listener) {
    _listener = listener;
  }

  late final _stockInfoBinaryThrottle = BinaryDelayThrottle(
    minInterval: duration,
    decode: (raw) {
      final decodedMessage = _handleDecodeMessage(raw);
      if (decodedMessage != null) _listener?.call(decodedMessage);
    },
  );

  late final _throttle = PerSymbolDelayThrottle2(
    batchMax: 2048,
    minInterval: duration,
    onData: (data) => _listener?.call(data),
  );

  void decodeMessage(dynamic data) {
    try {
      if (data is Uint8List && data.isNotEmpty) {
        final int channelId = data[0];

        if (channelId != BinaryChannel.stockInfo.value || data.length < 5) {
          return _pushPostThrottle(_handleDecodeMessage(data));
        }

        final symbol = _parseSymbol(data, from: 2, to: 5);

        if (symbol == null || symbol.isEmpty) {
          return _pushPostThrottle(_handleDecodeMessage(data));
        }

        _pushPostBinaryThrottle(channelId, symbol, data);
      } else if (data is Map<String, dynamic>) {
        _pushPostThrottle(data);
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  void _pushPostBinaryThrottle(int channel, String symbol, Uint8List raw) {
    final policy = postPolicyResolver?.call(channel);

    if (policy == null) {
      _stockInfoBinaryThrottle.gate(
        channel: channel,
        symbol: symbol,
        raw: raw,
      );
    } else if (policy == ThrottlePolicy.none) {
      final decoded = _handleDecodeMessage(raw);
      if (decoded != null) _listener?.call(decoded);
    } else {
      _stockInfoBinaryThrottle.gateOverride(
        channel: channel,
        symbol: symbol,
        raw: raw,
        interval: policy.interval,
      );
    }
  }

  void _pushPostThrottle(Map<String, dynamic>? decoded) {
    if (decoded == null) return;

    final channel = decoded['channel'] as String?;

    final policy = postPolicyResolver?.call(channel);

    if (policy == null) {
      _throttle.add(decoded);
    } else if (policy == ThrottlePolicy.none) {
      _listener?.call(decoded);
    } else {
      _throttle.addWithInterval(decoded, policy.interval);
    }
  }

  String? _parseSymbol(Uint8List data, {int from = 2, int to = 5}) {
    try {
      final subBytes = data.sublist(from, to);
      return utf8.decode(subBytes).trim();
    } catch (_, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }

    return null;
  }

  Map<String, dynamic>? _handleDecodeMessage(Uint8List data) {
    final channelId = data[0].toString();
    final messageBuffer = data.sublist(1);

    if (_schemas == null || !_schemas!.containsKey(channelId)) return null;

    final RecordType type = _schemas![channelId]!;

    final buffer = DataBuffer(messageBuffer.buffer);
    final decodedData = type.decode(buffer);
    decodedData['channel'] = type.name;

    return decodedData;
  }

  void reset() {
    _stockInfoBinaryThrottle.reset();
    _throttle.reset();
    _schemas = null;
  }

  void close() {
    _stockInfoBinaryThrottle.close();
    _throttle.close();
    _listener = null;
    _schemas = null;
  }
}
