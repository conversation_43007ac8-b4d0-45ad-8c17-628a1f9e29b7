import 'package:vp_socket/base/topic.dart';

sealed class VPInvestTopicKey extends SocketTopic<PERSON>ey {}

class SymbolChannelTopicKey extends VPInvestTopicKey {
  final String channel;
  final String symbol;

  SymbolChannelTopicKey({required this.channel, required this.symbol});

  @override
  List<Object?> get props => [channel, symbol];

  @override
  String toString() => 'SymbolChannelTopicKey($channel/$symbol)';
}
