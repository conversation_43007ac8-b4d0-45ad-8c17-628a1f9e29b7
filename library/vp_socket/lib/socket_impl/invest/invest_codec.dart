import 'package:vp_socket/codec/codec.dart';
import 'package:vp_socket/data/socket_data.dart';
import 'package:vp_socket/socket_impl/invest/invest_sub.dart';
import 'package:vp_socket/socket_impl/invest/invest_topic_key.dart';

class VPInvestCodec extends SocketCodec<VPInvestSub, VPInvestTopicKey> {
  @override
  List<VPInvestTopicKey> toTopicKeys(VPInvestSub s) => s.symbols
      .map((sym) => SymbolChannelTopicKey(channel: s.channel, symbol: sym))
      .toList();

  @override
  VPInvestSub? fromTopicKeys(List<VPInvestTopicKey> keys) {
    if (keys.isEmpty) return null;
    final first = keys.first as SymbolChannelTopicKey;
    final set = <String>{};
    for (final k in keys) {
      final kk = k as SymbolChannelTopicKey;
      if (kk.channel != first.channel) return null;
      set.add(kk.symbol);
    }
    return VPInvestSub(symbols: set, channel: first.channel);
  }

  @override
  List<VPInvestTopicKey> extractKeys(VPSocketData? d) {
    final ch = d?.channel, sy = d?.symbol;
    if (ch == null || sy == null) return const [];
    return [SymbolChannelTopicKey(channel: ch, symbol: sy)];
  }
}
