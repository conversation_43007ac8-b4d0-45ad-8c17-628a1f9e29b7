import 'package:vp_socket/base/base.dart';
import 'package:vp_socket/data/enum/socket_type.dart';
import 'package:vp_socket/socket_impl/account/socket_account_impl.dart';
import 'package:vp_socket/socket_impl/invest/isolate_invest_socket.dart';

class SocketFactory {
  static final Map<SocketType, BaseSocket> _sockets = {};

  static BaseSocket get(SocketType type) {
    return switch (type) {
      SocketType.stock =>
        _sockets.putIfAbsent(type, () => IsolateInvestSocket()),
      SocketType.account =>
        _sockets.putIfAbsent(type, () => VPSocketAccountConnect()),
    };
  }

  static void disposeAll() {
    for (final s in _sockets.values) {
      s.dispose();
    }
    _sockets.clear();
  }
}
