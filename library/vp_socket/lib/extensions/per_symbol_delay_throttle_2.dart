import 'dart:async';

class PerSymbolDelayThrottle2 {
  PerSymbolDelayThrottle2({
    required this.onData,
    this.minInterval = const Duration(milliseconds: 300),
    this.batchMax = 2048,
    this.keyBuilder,
  });

  final Duration minInterval;
  final int batchMax;

  /// Tạo key từ message (mặc định: "$channel|$symbol" hoặc chỉ "symbol")
  final String Function(Map<String, dynamic> msg)? keyBuilder;

  final Map<String, int> _lastEmitUs = {};
  final Map<String, Map<String, dynamic>> _pending = {};
  final Map<String, int> _dueUs = {};

  final Function(Map<String, dynamic>) onData;

  Timer? _timer;
  int? _nextDueUs;

  String _topicKey(Map<String, dynamic> m) {
    if (keyBuilder != null) return keyBuilder!(m);
    final sym = m['symbol'] as String? ?? '';
    final ch = m['channel'] as String?;
    return (ch == null || ch.isEmpty) ? sym : '$ch|$sym';
  }

  void add(dynamic msg) {
    if (msg is! Map<String, dynamic>) return;

    addWithInterval(msg, minInterval);
  }

  void addWithInterval(Map<String, dynamic> msg, Duration? interval) {
    final sym = msg['symbol'] as String?;
    if (sym == null || sym.isEmpty) {
      _emitBatch([msg]);
      return;
    }

    final key = _topicKey(msg);
    if (key.isEmpty) {
      _emitBatch([msg]);
      return;
    }

    final nowUs = DateTime.now().microsecondsSinceEpoch;
    final last = _lastEmitUs[key] ?? 0;
    final elapsed = nowUs - last;

    if (elapsed >= minInterval.inMicroseconds) {
      // đủ hạn -> emit ngay
      _emitBatch([msg]);
      _lastEmitUs[key] = nowUs;
      _pending.remove(key);
      _removeDue(key);
      return;
    }

    // chưa tới hạn -> coalesce & set due
    _pending[key] = msg;
    final due = last + minInterval.inMicroseconds;
    _setDue(key, due);
    _scheduleNext();
  }

  void _setDue(String key, int due) {
    _dueUs[key] = due;
    if (_nextDueUs == null || due < _nextDueUs!) {
      _nextDueUs = due; // cập nhật “min” nhanh
    }
  }

  void _removeDue(String key) {
    final removed = _dueUs.remove(key);
    if (removed != null && removed == _nextDueUs) {
      _nextDueUs = null; // sẽ tính lại lần tới
    }
  }

  void _scheduleNext() {
    if (_timer != null || _dueUs.isEmpty) return;

    final nowUs = DateTime.now().microsecondsSinceEpoch;
    final nextUs = _nextDueUs ?? _dueUs.values.reduce((a, b) => a < b ? a : b);
    _nextDueUs = nextUs; // cache

    final wait = Duration(microseconds: nextUs > nowUs ? nextUs - nowUs : 0);
    _timer = Timer(wait, _flushDue);
  }

  void _flushDue() {
    _timer?.cancel();
    _timer = null;

    if (_dueUs.isEmpty) return;

    final nowUs = DateTime.now().microsecondsSinceEpoch;
    final out = <Map<String, dynamic>>[];
    _nextDueUs = null;

    final dueKeys = <String>[];
    _dueUs.forEach((k, due) {
      if (due <= nowUs) {
        dueKeys.add(k);
      } else {
        if (_nextDueUs == null || due < _nextDueUs!) _nextDueUs = due;
      }
    });

    for (final k in dueKeys) {
      final m = _pending.remove(k);
      if (m != null) {
        out.add(m);
        _lastEmitUs[k] = nowUs;
      }
      _dueUs.remove(k);
      if (out.length >= batchMax) {
        _emitBatch(out);
        out.clear();
      }
    }

    if (out.isNotEmpty) _emitBatch(out);

    if (_dueUs.isNotEmpty) {
      _nextDueUs ??= _dueUs.values.reduce((a, b) => a < b ? a : b);
      _scheduleNext();
    }
  }

  void _emitBatch(List<Map<String, dynamic>> list) {
    onData.call({
      'type': 'dataBatch',
      'data': {'list': list}
    });
  }

  void reset() {
    _timer?.cancel();
    _timer = null;
    _lastEmitUs.clear();
    _pending.clear();
    _dueUs.clear();
    _nextDueUs = null;
  }

  void close() => reset();
}
