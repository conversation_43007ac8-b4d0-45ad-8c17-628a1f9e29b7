import 'package:vp_socket/base/socket_controller.dart';
import 'package:vp_socket/factory/socket_investment_factory.dart';
import 'package:vp_socket/socket_impl/invest/invest_sub.dart';
import 'package:vp_socket/socket_impl/invest/invest_topic_key.dart';

extension ControllerBatchExt
    on VPSocketController<VPInvestSub, VPInvestTopicKey> {
  void addBatch(List batch, {int chunk = 500}) {
    if (batch.isEmpty) return;
    int i = 0;

    void pump() {
      final end = (i + chunk < batch.length) ? i + chunk : batch.length;
      for (; i < end; i++) {
        final m = batch[i];
        if (m is Map<String, dynamic>) {
          add(defaultInvestmentSocketTransformData(m));
        }
      }
      if (i < batch.length) {
        Future.microtask(pump);
      }
    }

    pump();
  }
}
