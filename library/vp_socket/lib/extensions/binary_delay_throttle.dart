import 'dart:async';
import 'dart:typed_data';

class BinaryDelayThrottle {
  BinaryDelayThrottle({
    required this.decode,
    this.minInterval = const Duration(milliseconds: 300),
  });

  final Duration minInterval;
  final void Function(Uint8List raw) decode;

  final Map<String, int> _lastEmitUs = {};
  final Map<String, Uint8List> _pendingRaw = {};
  final Map<String, int> _dueUs = {};
  Timer? _timer;
  int? _nextDueUs;

  static String _k(int channel, String symbol) => '$channel|$symbol';

  bool gate({
    required int channel,
    required String symbol,
    required Uint8List raw,
  }) {
    return gateOverride(
      channel: channel,
      symbol: symbol,
      raw: raw,
      interval: minInterval,
    );
  }

  bool gateOverride({
    required int channel,
    required String symbol,
    required Uint8List raw,
    Duration? interval,
  }) {
    if (interval == null) {
      decode(raw);
      return true;
    }

    final key = _k(channel, symbol);
    final nowUs = DateTime.now().microsecondsSinceEpoch;
    final last = _lastEmitUs[key] ?? 0;
    final elapsed = nowUs - last;

    if (elapsed >= minInterval.inMicroseconds) {
      decode(raw);
      _lastEmitUs[key] = nowUs;
      _pendingRaw.remove(key);
      _removeDue(key);
      return true;
    }

    _pendingRaw[key] = raw;
    final due = last + minInterval.inMicroseconds;
    _setDue(key, due);
    _scheduleNext(nowUs);
    return false;
  }

  void _setDue(String key, int due) {
    _dueUs[key] = due;
    if (_nextDueUs == null || due < _nextDueUs!) {
      _nextDueUs = due;
    }
  }

  void _removeDue(String key) {
    final removed = _dueUs.remove(key);
    if (removed != null && removed == _nextDueUs) {
      _nextDueUs =
          _dueUs.isEmpty ? null : _dueUs.values.reduce((a, b) => a < b ? a : b);
    }
  }

  void _scheduleNext(int nowUs) {
    if (_timer != null || _dueUs.isEmpty) return;
    final nextUs = _nextDueUs ?? _dueUs.values.reduce((a, b) => a < b ? a : b);
    _nextDueUs = nextUs;
    final wait = Duration(microseconds: nextUs > nowUs ? nextUs - nowUs : 0);
    _timer =
        Timer(wait, () => _flushDue(DateTime.now().microsecondsSinceEpoch));
  }

  void _flushDue(int nowUs) {
    _timer?.cancel();
    _timer = null;
    if (_dueUs.isEmpty) return;

    _nextDueUs = null;
    final dueKeys = <String>[];
    _dueUs.forEach((k, due) {
      if (due <= nowUs) {
        dueKeys.add(k);
      } else {
        if (_nextDueUs == null || due < _nextDueUs!) _nextDueUs = due;
      }
    });

    for (final k in dueKeys) {
      final raw = _pendingRaw.remove(k);
      if (raw != null) {
        decode(raw);
        _lastEmitUs[k] = nowUs;
      }
      _dueUs.remove(k);
    }

    if (_dueUs.isNotEmpty) {
      _scheduleNext(nowUs);
    }
  }

  void reset() {
    _timer?.cancel();
    _timer = null;
    _nextDueUs = null;
    _dueUs.clear();
    _lastEmitUs.clear();
    _pendingRaw.clear();
  }

  void close() => reset();
}
