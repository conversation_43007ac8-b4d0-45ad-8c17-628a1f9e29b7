class ThrottleRule {
  ThrottleRule({
    required this.channel,
    required this.symbol,
    required this.intervalMs,
  });

  final String channel;
  final String symbol;
  final int intervalMs;

  Map<String, dynamic> toJson() => {
    'channel': channel,
    'symbol': symbol,
    'intervalMs': intervalMs,
  };

  static ThrottleRule fromJson(Map<String, dynamic> j) => ThrottleRule(
    channel: j['channel'] as String,
    symbol: j['symbol'] as String,
    intervalMs: j['intervalMs'] as int,
  );
}
