class DisconnectInfo {
  final int? code;
  final String? reason;
  final Object? error;
  final StackTrace? stackTrace;
  final bool willRetry;
  final bool isAuthFailure;
  final bool initiatedByClient;

  const DisconnectInfo({
    this.code,
    this.reason,
    this.error,
    this.stackTrace,
    this.willRetry = false,
    this.isAuthFailure = false,
    this.initiatedByClient = false,
  });
}
