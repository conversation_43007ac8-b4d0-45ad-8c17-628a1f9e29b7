import 'dart:isolate';

/// A method invocation message
class MethodInvocation {
  /// Null result placeholder since null cannot be sent to URI isolates
  static const nullResult = '_isolate_channel.null';

  /// The method to invoke
  final String method;

  /// The arguments to pass to the method
  final dynamic arguments;

  /// The port to respond to
  ///
  /// If null, the method is not expected to respond
  final SendPort? respond;

  /// Constructor
  const MethodInvocation({
    required this.method,
    this.arguments,
    this.respond,
  });

  /// From json
  factory MethodInvocation.fromJson(Map<String, dynamic> json) {
    return MethodInvocation(
      method: json['method'],
      arguments: json['arguments'],
      respond: json['sendPort'],
    );
  }

  /// To json
  Map<String, dynamic> toJson() {
    return {
      'method': method,
      if (arguments != null) 'arguments': arguments,
      if (respond != null) 'sendPort': respond,
    };
  }
}
