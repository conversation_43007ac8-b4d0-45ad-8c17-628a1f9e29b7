import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:flutter/cupertino.dart';
import 'package:vp_socket/base/web_socket_config.dart';
import 'package:vp_socket/data/disconnect_info.dart';
import 'package:vp_socket/data/enum/connection_state.dart';
import 'package:vp_socket/data/enum/socket_event.dart';
import 'package:vp_socket/data/socket_context.dart';
import 'package:vp_socket/base/base.dart';
import 'package:vp_socket/interceptor/socket_interceptor.dart';
import 'package:web_socket_channel/io.dart';

abstract class VPBaseSocket extends BaseSocket with InterceptableSocket {
  VPBaseSocket({
    this.token,
    this.authenticationRequired = false,
    this.pingInterval = const Duration(seconds: 20),
    this.refreshToken,
  });

  String get baseUrl;

  String get path;

  String? token;

  final bool authenticationRequired;

  final Duration pingInterval;

  final Future<String?> Function()? refreshToken;

  IOWebSocketChannel? _socket;
  StreamSubscription? _sub;
  final _stateCtrl = StreamController<ConnectionStateX>.broadcast();

  @override
  Stream<ConnectionStateX> get stateStream => _stateCtrl.stream;

  ConnectionStateX _preState = ConnectionStateX.idle;
  ConnectionStateX _state = ConnectionStateX.idle;

  @override
  ConnectionStateX get state => _state;

  Timer? _retryTimer;
  int _retryCount = 0;
  bool _disconnectNotified = false;

  bool Function()? canRetryPredicate;

  static const Duration _initialDelay = Duration(seconds: 1);
  static const Duration _maxDelay = Duration(seconds: 5);

  bool get isDisposed => _state == ConnectionStateX.disposed;

  bool get isConnecting =>
      _state == ConnectionStateX.connecting ||
      _state == ConnectionStateX.reconnecting;

  bool get isConnected => _state == ConnectionStateX.connected;

  bool get invalidToken =>
      authenticationRequired && (token == null || token!.isEmpty);

  // context
  String? _connId;
  SocketContext? socketCtx;

  void onConnected() {}

  void onTimeout() {}

  void onConnecting() {}

  void onDisconnect(DisconnectInfo info) {}

  void onError(Object error, {String? debugName, StackTrace? stackTrace}) {}

  void transformData(dynamic rawData);

  void _notifyDisconnectOnce(DisconnectInfo info) {
    if (_disconnectNotified || isDisposed) return;

    _disconnectNotified = true;

    _emitEvent(
      SocketEvent.disconnecting,
      ctx: socketCtx?.copyWith(
        closeReason: info.reason,
        attempt: _retryCount,
        delayMs: info.willRetry ? (_retryTimer == null ? 0 : null) : null,
      ),
      payload: {
        'code': info.code,
        'reason': info.reason,
        'willRetry': info.willRetry,
        'auth': info.isAuthFailure,
        'client': info.initiatedByClient,
      },
    );

    onDisconnect(info);
  }

  Future<bool>? _emitEvent(
    SocketEvent event, {
    SocketContext? ctx,
    Object? payload,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (socketCtx == null && ctx == null) return null;

    socketCtx = ctx ?? socketCtx;

    return emitEvent(
      event,
      socketCtx!,
      payload: payload,
      error: error,
      stackTrace: stackTrace,
    );
  }

  void _emitState(ConnectionStateX state) {
    if (_state == state) return;

    _preState = _state;

    _state = state;

    if (!_stateCtrl.isClosed) _stateCtrl.add(state);

    _emitEvent(
      SocketEvent.stateChange,
      ctx: socketCtx?.copyWith(state: state.name),
      payload: {
        'prevState': _preState.name,
        'currentState': _state.name,
      },
    );
  }

  @override
  Future<void> connect() async {
    if (isDisposed || isConnecting || isConnected || invalidToken) return;

    await _connectInternal(isReconnect: false);
  }

  @override
  Future<void> reconnect() async {
    if (isDisposed || isConnecting || invalidToken) return;
    await _closeInternal(autoRetry: false);
    await _connectInternal(isReconnect: true);
  }

  @override
  Future<void> close() async {
    await _closeInternal(autoRetry: false);
  }

  @override
  Future<void> dispose() async {
    _retryTimer?.cancel();
    _retryTimer = null;
    await _closeInternal(autoRetry: false, toState: ConnectionStateX.disposed);
    await _stateCtrl.close();
  }

  Future<void> _connectInternal({required bool isReconnect}) async {
    _retryTimer?.cancel();
    _retryTimer = null;

    _connId = _randId();
    _disconnectNotified = false;

    socketCtx = SocketContext(
      socketType: runtimeType.toString(),
      baseUrl: baseUrl,
      path: path,
      connId: _connId!,
      state: isReconnect
          ? ConnectionStateX.reconnecting.name
          : ConnectionStateX.connecting.name,
    );

    _emitState(isReconnect
        ? ConnectionStateX.reconnecting
        : ConnectionStateX.connecting);

    onConnecting();

    await _emitEvent(SocketEvent.connectStart);

    try {
      _socket = await SocketConfig.configUrl(
        url: baseUrl,
        path: path,
        token: token,
      );

      _attachListeners();
      _retryCount = 0;

      _emitState(ConnectionStateX.connected);

      await _emitEvent(SocketEvent.connectSuccess);

      onConnected();
    } catch (e, st) {
      _emitState(ConnectionStateX.closed);

      await _emitEvent(SocketEvent.connectFail, error: e, stackTrace: st);

      _handleError(e, stack: st, scheduleRetry: true);
    }
  }

  void _attachListeners() {
    _sub?.cancel();
    _sub = _socket?.stream.listen(
      (msg) {
        transformData(msg);
      },
      onError: (e, st) {
        _emitEvent(SocketEvent.error, error: e, stackTrace: st);

        _handleError(e, stack: st, scheduleRetry: true);
      },
      onDone: _onDoneScheduleRetryIfNeeded,
      cancelOnError: true,
    );
  }

  Future<void> _closeInternal({
    required bool autoRetry,
    ConnectionStateX toState = ConnectionStateX.closed,
  }) async {
    _retryTimer?.cancel();
    _retryTimer = null;

    _emitState(ConnectionStateX.closing);

    _notifyDisconnectOnce(DisconnectInfo(
      code: WebSocketStatus.normalClosure,
      reason: toState.name,
      willRetry: autoRetry,
      initiatedByClient: true,
    ));

    await _sub?.cancel();
    _sub = null;

    await _socket?.sink.close(WebSocketStatus.normalClosure);
    _socket = null;

    _emitState(toState);

    _emitEvent(
      SocketEvent.connectionClosed,
      ctx: socketCtx?.copyWith(closeReason: toState.name),
    );

    if (autoRetry && !isDisposed && toState != ConnectionStateX.disposed) {
      _scheduleRetry();
    }
  }

  void _onDoneScheduleRetryIfNeeded() async {
    if (isDisposed ||
        _state == ConnectionStateX.closing ||
        _state == ConnectionStateX.closed) {
      _emitState(ConnectionStateX.closed);
      return;
    }

    final code = _socket?.closeCode;
    final reason = _socket?.closeReason;

    if (_isAuthClose(code, reason)) {
      if (refreshToken != null) {
        final newToken = await refreshToken!();
        final willRetry = newToken?.isNotEmpty == true;

        _notifyDisconnectOnce(DisconnectInfo(
          code: code,
          reason: reason,
          willRetry: willRetry,
          isAuthFailure: true,
          initiatedByClient: false,
        ));

        if (willRetry) {
          token = newToken;
          _scheduleRetry();
          return;
        }
      }
      onError(Exception('Auth failure: $code $reason'), debugName: '$reason');
      await _closeInternal(autoRetry: false, toState: ConnectionStateX.closed);
      return;
    }

    final willRetry = _shouldRetry(code);

    _notifyDisconnectOnce(DisconnectInfo(
      code: code,
      reason: reason,
      willRetry: willRetry,
      initiatedByClient: false,
    ));

    if (willRetry) {
      _scheduleRetry();
    } else {
      await _closeInternal(autoRetry: false, toState: ConnectionStateX.closed);
    }
  }

  bool _isAuthClose(int? code, String? reason) {
    if (code == 4001 || code == 4401) return true;
    final r = (reason ?? '').toLowerCase();
    return r.contains('auth') ||
        r.contains('unauthorized') ||
        r.contains('token');
  }

  bool _shouldRetry(int? code) {
    return code == null || code != WebSocketStatus.normalClosure;
  }

  void _handleError(
    Object e, {
    StackTrace? stack,
    required bool scheduleRetry,
  }) {
    debugPrintStack(stackTrace: stack);

    _notifyDisconnectOnce(DisconnectInfo(
      code: _socket?.closeCode,
      reason: _socket?.closeReason,
      error: e,
      stackTrace: stack,
      willRetry: scheduleRetry && !isDisposed,
      initiatedByClient: false,
    ));

    if (isConnecting) _emitState(ConnectionStateX.closed);

    if (e is TimeoutException) {
      onTimeout();
    } else {
      onError(e, stackTrace: stack);
    }

    if (scheduleRetry && !isDisposed) _scheduleRetry();
  }

  void _scheduleRetry() {
    /// prevent retry when app in background or no internet
    if (canRetryPredicate != null && !canRetryPredicate!()) {
      return;
    }

    if (isDisposed || isConnected || isConnecting) return;

    if (_retryTimer != null) return;

    _retryCount++;
    final base =
        _retryCount <= 3 ? _initialDelay * (1 << (_retryCount - 1)) : _maxDelay;
    final jitter = Duration(milliseconds: Random().nextInt(300));
    final delay = base + jitter;

    _emitEvent(
      SocketEvent.retryScheduled,
      ctx: socketCtx?.copyWith(
        attempt: _retryCount,
        delayMs: delay.inMilliseconds,
      ),
    );

    _retryTimer = Timer(delay, () async {
      _retryTimer = null;

      _emitEvent(
        SocketEvent.retryStart,
        ctx: socketCtx?.copyWith(attempt: _retryCount),
      );

      if (!isDisposed && !invalidToken) {
        await reconnect();
      }
    });
  }

  Future<void> send(Object data) async {
    if (isDisposed) return;

    final c = socketCtx;
    if (c == null) return;

    final consumed = await emitEvent(SocketEvent.messageOut, c, payload: data);
    if (consumed) return;

    _socket?.sink.add(data);
  }

  String _randId() =>
      List.generate(8, (_) => Random().nextInt(36).toRadixString(36)).join();
}
