import 'dart:async';
import 'dart:io';
import 'package:flutter/widgets.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:vp_socket/base/base.dart';

mixin SocketLifecycleConnectivityMixin on BaseSocket{
  var appInBackground = false;
  var networkAvailable = true;

  bool get isReconnectAllowed => !appInBackground && networkAvailable;

  void closeNoRetry() {}

  void connectNow() {}

  void _ensurePolicyConnect() {
    if (!appInBackground && networkAvailable) {
      connectNow();
    }
  }

  Future<void> appBackground() async {
    appInBackground = true;
    closeNoRetry();
  }

  Future<void> appForeground() async {
    appInBackground = false;
    _ensurePolicyConnect();
  }

  Future<void> networkDown() async {
    networkAvailable = false;
    closeNoRetry();
  }

  Future<void> networkUp() async {
    networkAvailable = true;
    _ensurePolicyConnect();
  }
}

class SocketLifecycleConnectivityObserver with WidgetsBindingObserver {
  SocketLifecycleConnectivityObserver(
    this.socket, {
    this.debounce = const Duration(milliseconds: 400),
  });

  final SocketLifecycleConnectivityMixin socket;
  final Duration debounce;

  StreamSubscription? _connSub;

  Timer? _netDebounce, _appDebounce;

  bool _hasConnection = true;
  bool _stopped = false;

  AppLifecycleState? _appLifecycleState;

  Future<void> start() async {
    WidgetsBinding.instance.addObserver(this);

    _stopped = false;

    _appLifecycleState = WidgetsBinding.instance.lifecycleState;

    final initial = await Connectivity().checkConnectivity();
    final isNone = initial.contains(ConnectivityResult.none);
    _hasConnection = isNone ? false : await _checkInternetConnection();

    _connSub = Connectivity().onConnectivityChanged.listen((results) async {
      await _handleNetworkChange(results);
    });
  }

  void stop() {
    WidgetsBinding.instance.removeObserver(this);

    _stopped = true;
    _connSub?.cancel();
    _connSub = null;
    _netDebounce?.cancel();
    _netDebounce = null;
    _appDebounce?.cancel();
    _appDebounce = null;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_stopped) return;
    final prev = _appLifecycleState;
    if (prev == state) return;

    if (state == AppLifecycleState.resumed &&
        prev != AppLifecycleState.resumed) {
      _scheduleAppDebounced(() => socket.appForeground());
    } else if (state != AppLifecycleState.resumed &&
        prev == AppLifecycleState.resumed) {
      _scheduleAppDebounced(() => socket.appBackground());
    }
    _appLifecycleState = state;
  }

  Future _handleNetworkChange(List<ConnectivityResult> result) async {
    if (_stopped) return;

    final disconnected = result.contains(ConnectivityResult.none);

    if (disconnected) {
      _hasConnection = false;
      _scheduleNetDebounced(() => socket.networkDown());
      return;
    }

    final ok = await _checkInternetConnection();
    if (ok != _hasConnection) {
      _hasConnection = ok;
      _scheduleNetDebounced(
          () => ok ? socket.networkUp() : socket.networkDown());
    }
  }

  void _scheduleNetDebounced(void Function() f) {
    _netDebounce?.cancel();
    _netDebounce = Timer(debounce, () {
      if (!_stopped) f();
    });
  }

  void _scheduleAppDebounced(void Function() f) {
    _appDebounce?.cancel();
    _appDebounce = Timer(debounce, () {
      if (!_stopped) f();
    });
  }

  Future<bool> _checkInternetConnection() async {
    try {
      final r = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 2));
      return r.isNotEmpty && r[0].rawAddress.isNotEmpty;
    } catch (_) {
      return false;
    }
  }
}
