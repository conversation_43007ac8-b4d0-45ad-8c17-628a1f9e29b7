import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

class CheckBoxWidget extends StatefulWidget {
  bool isChecked = false;

  CheckBoxWidget(this.isChecked, {super.key, this.onSelected});

  VoidCallback? onSelected;

  @override
  _CustomCheckBoxState createState() => _CustomCheckBoxState();
}

class _CustomCheckBoxState extends State<CheckBoxWidget> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          GestureDetector(
            onTap:
                widget.onSelected ??
                () {
                  setState(() {
                    widget.isChecked = !widget.isChecked;
                  });
                },
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                border: Border.all(
                  color:
                      widget.isChecked ? themeData.primary : themeData.gray700,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Container(
                width: 10,
                height: 10,
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(18),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        widget.isChecked
                            ? themeData.primary
                            : themeData.bgPopup,
                    borderRadius: BorderRadius.circular(18),
                  ),
                ),
              ),
            ),
          ),
          // const SizedBox(
          //   width: 10,
          // ),
          // Text(widget.title,
          //     style: vpTextStyle.body14?
          //         .copyWith(color: ColorUtils.gray700)),
        ],
      ),
    );
  }
}
