import 'package:flutter_test/flutter_test.dart';
import 'package:vp_trading/cubit/derivative/derivatives_order_book_multi_select/derivatives_order_book_multi_select_cubit.dart';
import 'package:vp_trading/model/order/order_book_model.dart';

void main() {
  group('DerivativesOrderBookMultiSelectCubit', () {
    late DerivativesOrderBookMultiSelectCubit cubit;

    setUp(() {
      cubit = DerivativesOrderBookMultiSelectCubit();
    });

    tearDown(() {
      cubit.close();
    });

    group('toggleOrderSelection', () {
      test('should not allow selection when not in multi-select mode', () {
        // Arrange
        expect(cubit.state.isMultiSelectMode, false);

        // Act
        cubit.toggleOrderSelection('order1');

        // Assert
        expect(cubit.state.selectedOrderIds, isEmpty);
      });

      test('should allow selection when in multi-select mode and order can be cancelled', () {
        // Arrange
        cubit.enterMultiSelectMode();
        final orders = [
          const OrderBookModel(orderId: 'order1', allowCancel: 'Y'),
        ];

        // Act
        cubit.toggleOrderSelection('order1', orders);

        // Assert
        expect(cubit.state.selectedOrderIds, contains('order1'));
      });

      test('should not allow selection when order cannot be cancelled', () {
        // Arrange
        cubit.enterMultiSelectMode();
        final orders = [
          const OrderBookModel(orderId: 'order1', allowCancel: 'N'),
        ];

        // Act
        cubit.toggleOrderSelection('order1', orders);

        // Assert
        expect(cubit.state.selectedOrderIds, isEmpty);
      });

      test('should deselect order when already selected', () {
        // Arrange
        cubit.enterMultiSelectMode();
        final orders = [
          const OrderBookModel(orderId: 'order1', allowCancel: 'Y'),
        ];
        
        // First select the order
        cubit.toggleOrderSelection('order1', orders);
        expect(cubit.state.selectedOrderIds, contains('order1'));

        // Act - toggle again to deselect
        cubit.toggleOrderSelection('order1', orders);

        // Assert
        expect(cubit.state.selectedOrderIds, isEmpty);
      });

      test('should allow selection without order validation when allOrders is null', () {
        // Arrange
        cubit.enterMultiSelectMode();

        // Act
        cubit.toggleOrderSelection('order1');

        // Assert
        expect(cubit.state.selectedOrderIds, contains('order1'));
      });

      test('should handle multiple order selections correctly', () {
        // Arrange
        cubit.enterMultiSelectMode();
        final orders = [
          const OrderBookModel(orderId: 'order1', allowCancel: 'Y'),
          const OrderBookModel(orderId: 'order2', allowCancel: 'Y'),
          const OrderBookModel(orderId: 'order3', allowCancel: 'N'),
        ];

        // Act
        cubit.toggleOrderSelection('order1', orders);
        cubit.toggleOrderSelection('order2', orders);
        cubit.toggleOrderSelection('order3', orders); // Should not be selected

        // Assert
        expect(cubit.state.selectedOrderIds, hasLength(2));
        expect(cubit.state.selectedOrderIds, contains('order1'));
        expect(cubit.state.selectedOrderIds, contains('order2'));
        expect(cubit.state.selectedOrderIds, isNot(contains('order3')));
      });
    });

    group('enterMultiSelectMode', () {
      test('should enter multi-select mode and clear selections', () {
        // Act
        cubit.enterMultiSelectMode();

        // Assert
        expect(cubit.state.isMultiSelectMode, true);
        expect(cubit.state.selectedOrderIds, isEmpty);
      });

      test('should exit multi-select mode when already in multi-select mode', () {
        // Arrange
        cubit.enterMultiSelectMode();
        expect(cubit.state.isMultiSelectMode, true);

        // Act
        cubit.enterMultiSelectMode();

        // Assert
        expect(cubit.state.isMultiSelectMode, false);
      });
    });

    group('exitMultiSelectMode', () {
      test('should exit multi-select mode and clear all state', () {
        // Arrange
        cubit.enterMultiSelectMode();
        cubit.toggleOrderSelection('order1');
        expect(cubit.state.isMultiSelectMode, true);
        expect(cubit.state.selectedOrderIds, isNotEmpty);

        // Act
        cubit.exitMultiSelectMode();

        // Assert
        expect(cubit.state.isMultiSelectMode, false);
        expect(cubit.state.selectedOrderIds, isEmpty);
        expect(cubit.state.isCancelling, false);
        expect(cubit.state.errorMessage, null);
      });
    });

    group('isOrderSelected', () {
      test('should return true for selected orders', () {
        // Arrange
        cubit.enterMultiSelectMode();
        cubit.toggleOrderSelection('order1');

        // Act & Assert
        expect(cubit.isOrderSelected('order1'), true);
        expect(cubit.isOrderSelected('order2'), false);
      });
    });

    group('selectedCount', () {
      test('should return correct count of selected orders', () {
        // Arrange
        cubit.enterMultiSelectMode();

        // Act & Assert
        expect(cubit.selectedCount, 0);

        cubit.toggleOrderSelection('order1');
        expect(cubit.selectedCount, 1);

        cubit.toggleOrderSelection('order2');
        expect(cubit.selectedCount, 2);

        cubit.toggleOrderSelection('order1'); // Deselect
        expect(cubit.selectedCount, 1);
      });
    });
  });
}
