import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:vp_trading/cubit/derivative/derivatives_order_book/derivatives_order_book_cubit.dart';
import 'package:vp_trading/model/order/order_book_model.dart';

void main() {
  group('DerivativesOrderBookCubit', () {
    late DerivativesOrderBookCubit cubit;

    setUp(() {
      cubit = DerivativesOrderBookCubit();
    });

    tearDown(() {
      cubit.close();
    });

    group('updateEnableCancelButton', () {
      test('should enable cancel button when at least one order can be cancelled', () {
        // Arrange
        final orders = [
          const OrderBookModel(orderId: '1', allowCancel: 'N'),
          const OrderBookModel(orderId: '2', allowCancel: 'Y'),
          const OrderBookModel(orderId: '3', allowCancel: 'N'),
        ];

        // Act
        cubit.emit(cubit.state.copyWith(listItems: orders));
        cubit.updateEnableCancelButton();

        // Assert
        expect(cubit.state.isEnableCancelButton, true);
      });

      test('should disable cancel button when no orders can be cancelled', () {
        // Arrange
        final orders = [
          const OrderBookModel(orderId: '1', allowCancel: 'N'),
          const OrderBookModel(orderId: '2', allowCancel: 'N'),
          const OrderBookModel(orderId: '3', allowCancel: 'N'),
        ];

        // Act
        cubit.emit(cubit.state.copyWith(listItems: orders));
        cubit.updateEnableCancelButton();

        // Assert
        expect(cubit.state.isEnableCancelButton, false);
      });

      test('should disable cancel button when order list is empty', () {
        // Arrange
        final orders = <OrderBookModel>[];

        // Act
        cubit.emit(cubit.state.copyWith(listItems: orders));
        cubit.updateEnableCancelButton();

        // Assert
        expect(cubit.state.isEnableCancelButton, false);
      });

      test('should enable cancel button when all orders can be cancelled', () {
        // Arrange
        final orders = [
          const OrderBookModel(orderId: '1', allowCancel: 'Y'),
          const OrderBookModel(orderId: '2', allowCancel: 'Y'),
          const OrderBookModel(orderId: '3', allowCancel: 'Y'),
        ];

        // Act
        cubit.emit(cubit.state.copyWith(listItems: orders));
        cubit.updateEnableCancelButton();

        // Assert
        expect(cubit.state.isEnableCancelButton, true);
      });
    });

    group('setMultiChoice', () {
      test('should set multi-choice mode to true', () {
        // Act
        cubit.setMultiChoice(true);

        // Assert
        expect(cubit.state.isMultiChoice, true);
      });

      test('should set multi-choice mode to false', () {
        // Act
        cubit.setMultiChoice(false);

        // Assert
        expect(cubit.state.isMultiChoice, false);
      });
    });

    group('toggleMultiChoice', () {
      test('should toggle multi-choice mode from false to true', () {
        // Arrange
        expect(cubit.state.isMultiChoice, false);

        // Act
        cubit.toggleMultiChoice();

        // Assert
        expect(cubit.state.isMultiChoice, true);
      });

      test('should toggle multi-choice mode from true to false', () {
        // Arrange
        cubit.setMultiChoice(true);
        expect(cubit.state.isMultiChoice, true);

        // Act
        cubit.toggleMultiChoice();

        // Assert
        expect(cubit.state.isMultiChoice, false);
      });
    });
  });
}
