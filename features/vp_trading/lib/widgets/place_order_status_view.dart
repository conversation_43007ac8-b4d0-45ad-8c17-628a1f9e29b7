import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class PlaceOrderStatusView extends StatelessWidget {
  const PlaceOrderStatusView({
    required this.builder,
    required this.apiStatus,
    this.child,
    this.height,
    super.key,
  });

  final Widget Function(BuildContext context, Widget? child) builder;

  final ApiStatus apiStatus;

  final double? height;

  final Widget? child;

  @override
  Widget build(BuildContext context) {
    if (apiStatus.isError) {
      return SizedBox(height: height, child: const VPErrorView());
    }

    return Stack(
      alignment: Alignment.center,
      children: [
        builder(context, child),
        if (apiStatus.isLoading) const VPInnerLoading(),
      ],
    );
  }
}
