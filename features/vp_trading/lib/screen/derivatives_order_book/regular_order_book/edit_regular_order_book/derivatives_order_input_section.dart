import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/utils/text_inputformater.dart';

import 'derivatives_order_input_field.dart';

/// Widget chứa các input fields (quantity và price) với validation
class DerivativesOrderInputSection extends StatelessWidget {
  final TextEditingController quantityController;
  final TextEditingController priceController;
  final FocusNode quantityFocusNode;
  final FocusNode priceFocusNode;
  final Function(int) onQuantityUpdate;
  final Function(double) onPriceUpdate;
  final ValidateOrderCubit validateOrderCubit;
  final OrderBookModel item;
  final int quantity;
  final double price;

  const DerivativesOrderInputSection({
    super.key,
    required this.quantityController,
    required this.priceController,
    required this.quantityFocusNode,
    required this.priceFocusNode,
    required this.onQuantityUpdate,
    required this.onPriceUpdate,
    required this.validateOrderCubit,
    required this.item,
    required this.quantity,
    required this.price,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Quantity input with +/- buttons
        BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
          bloc: validateOrderCubit,
          builder: (context, state) {
            return DerivativesOrderInputField(
              controller: quantityController,
              label: 'Số lượng',
              focusNode: quantityFocusNode,
              onDecrease: () => onQuantityUpdate(quantity - 1),
              onIncrease: () => onQuantityUpdate(quantity + 1),
              onChanged: (value) {
                final newValue = int.tryParse(value);
                if (newValue != null && newValue >= 0) {
                  onQuantityUpdate(newValue);
                }
              },
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                ...volumeInputFormatter,
              ],
              keyboardType: TextInputType.number,
              errorText:
                  state.errorVolume.isEditOrderError
                      ? state.errorVolume.messageEditOrder(
                        validateOrderCubit.maxVolume().toString(),
                        (item.qty ?? 0) < 100,
                      )
                      : null,
            );
          },
        ),
        const SizedBox(height: 8),

        // Price input with +/- buttons
        BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
          bloc: validateOrderCubit,
          builder: (context, state) {
            return DerivativesOrderInputField(
              controller: priceController,
              label: 'Giá đặt',
              focusNode: priceFocusNode,
              onDecrease:
                  () =>
                      onPriceUpdate((price - 0.5).clamp(0.0, double.infinity)),
              onIncrease: () => onPriceUpdate(price + 0.5),
              onChanged: (value) {
                final newValue = double.tryParse(value);
                if (newValue != null && newValue >= 0) {
                  onPriceUpdate(newValue);
                }
              },
              inputFormatters: [
                removeZeroStartInputFormatter,
                ...priceInputFormatter,
              ],
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              errorText:
                  state.errorPrice.isErrorEditOrder
                      ? state.errorPrice.messageEditOrder
                      : null,
            );
          },
        ),
      ],
    );
  }
}
