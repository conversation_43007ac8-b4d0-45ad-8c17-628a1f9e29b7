import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

/// Reusable input field widget với +/- buttons cho derivatives order dialog
class DerivativesOrderInputField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final FocusNode focusNode;
  final VoidCallback onDecrease;
  final VoidCallback onIncrease;
  final ValueChanged<String> onChanged;
  final List<TextInputFormatter> inputFormatters;
  final TextInputType keyboardType;
  final String? errorText;

  const DerivativesOrderInputField({
    super.key,
    required this.controller,
    required this.label,
    required this.focusNode,
    required this.onDecrease,
    required this.onIncrease,
    required this.onChanged,
    required this.inputFormatters,
    required this.keyboardType,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                label,
                style: context.textStyle.body14?.copyWith(
                  color: context.colors.textSecondary,
                ),
              ),
            ),
            const SizedBox(width: 16),
            SizedBox(
              width: 200,
              child: VPTextField(
                controller: controller,
                textAlign: TextAlign.center,
                keyboardType: keyboardType,
                focusNode: focusNode,
                inputFormatters: inputFormatters,
                onChanged: onChanged,
                inputType: errorText != null ? InputType.error : InputType.rest,
                style: context.textStyle.body14?.copyWith(
                  color: context.colors.textPrimary,
                ),
                prefixIcon:
                    (color) => IconButton(
                      padding: const EdgeInsets.all(0),
                      icon: Icon(
                        Icons.remove,
                        color: context.colors.iconPrimary,
                        size: 16,
                      ),
                      onPressed: onDecrease,
                    ),
                suffixIcon:
                    (color) => IconButton(
                      padding: const EdgeInsets.all(0),
                      icon: Icon(
                        Icons.add,
                        color: context.colors.iconPrimary,
                        size: 16,
                      ),
                      onPressed: onIncrease,
                    ),
                caption:
                    errorText != null
                        ? (color) => Text(
                          errorText!,
                          style: context.textStyle.captionRegular?.copyWith(
                            color: context.colors.textAccentRed,
                          ),
                        )
                        : null,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
