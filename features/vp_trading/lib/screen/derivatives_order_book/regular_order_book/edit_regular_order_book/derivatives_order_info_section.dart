import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

/// Widget hiển thị thông tin đơn hàng trong dialog sửa lệnh derivatives
class DerivativesOrderInfoSection extends StatelessWidget {
  final OrderBookModel item;

  const DerivativesOrderInfoSection({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Order Type (read-only)
        _buildInfoRow(
          context,
          label: 'Loại lệnh',
          value: item.orderTypeEnum == OrderTypeEnum.buy ? 'LONG' : 'SHORT',
          valueColor:
              item.orderTypeEnum == OrderTypeEnum.buy
                  ? context.colors.textAccentGreen
                  : context.colors.textAccentRed,
        ),
        const SizedBox(height: 8),

        // Contract Code (read-only)
        _buildInfoRow(context, label: 'Mã hợp đồng', value: item.symbol ?? '-'),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Row(
      children: [
        Expanded(
          child: Text(
            label,
            style: context.textStyle.body14?.copyWith(
              color: context.colors.textSecondary,
            ),
          ),
        ),
        Text(
          value,
          style: context.textStyle.subtitle14?.copyWith(
            color: valueColor ?? context.colors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
