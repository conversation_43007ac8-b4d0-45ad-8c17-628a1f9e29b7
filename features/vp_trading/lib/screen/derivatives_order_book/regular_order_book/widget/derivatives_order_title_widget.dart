import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/l10n.dart';

class DerivativesOrderTitleWidget extends StatelessWidget {
  final List<int> expandTitleWidget;
  final bool showTitleDeleteAll;
  final VoidCallback? onDeleteAll;
  final VoidCallback? onMultiSelectMode;
  final bool isEnableCancelButton;

  const DerivativesOrderTitleWidget({
    super.key,
    required this.expandTitleWidget,
    this.showTitleDeleteAll = false,
    this.onDeleteAll,
    this.onMultiSelectMode,
    this.isEnableCancelButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (showTitleDeleteAll) ...[
          Row(
            children: [
              Expanded(
                child: VpsButton.secondaryDangerXsSmall(
                  title: VPTradingLocalize.current.trading_cancel_all_order,
                  onPressed: onDeleteAll,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: VpsButton.secondaryDangerXsSmall(
                  title: 'Hủy nhiều',
                  onPressed: isEnableCancelButton ? onMultiSelectMode : null,
                  disabled: !isEnableCancelButton,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
        ],
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  'Mã HĐ/ Trạng thái',
                  style: context.textStyle.captionRegular?.copyWith(
                    color: vpColor.textSecondary,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: Text(
                  'KL đặt/ Giá đặt',
                  style: context.textStyle.captionRegular?.copyWith(
                    color: vpColor.textSecondary,
                  ),
                  textAlign: TextAlign.left,
                ),
              ),

              Expanded(
                flex: 1,
                child: Text(
                  'Thao tác',
                  style: context.textStyle.captionRegular?.copyWith(
                    color: vpColor.textSecondary,
                  ),
                  textAlign: TextAlign.right,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
