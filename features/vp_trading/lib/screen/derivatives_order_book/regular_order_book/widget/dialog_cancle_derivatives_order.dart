import 'package:flutter/material.dart';
import 'package:vp_common/generated/l10n.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

void dialogConfirmDeleteDerivativesOrders(
  BuildContext context,
  OrderBookModel item,
  VoidCallback onConfirmCallback,
) async {
  VPPopup.custom(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Hủy lệnh',
              style: context.textStyle.headineBold6?.copyWith(
                color: vpColor.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Information section
            Column(
              children: [
                _buildInfoRow(
                  context,
                  label: '<PERSON><PERSON> hợp đồng',
                  value: item.symbol ?? '-',
                ),
                const SizedBox(height: 8),
                _buildInfoRow(
                  context,
                  label: 'Loại lệnh',
                  value:
                      item.orderTypeEnum == OrderTypeEnum.buy
                          ? 'LONG'
                          : 'SHORT',
                  valueColor:
                      item.orderTypeEnum == OrderTypeEnum.buy
                          ? vpColor.textAccentGreen
                          : vpColor.textAccentRed,
                ),
                const SizedBox(height: 8),
                _buildInfoRow(
                  context,
                  label: 'Khối lượng',
                  value: '${item.qty ?? 0}',
                ),
                const SizedBox(height: 8),
                _buildInfoRow(
                  context,
                  label: 'Giá đặt',
                  value: _getPriceDisplayValue(item),
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],
        ),
      )
      .copyWith(
        button: VpsButton.secondaryXsSmall(
          title: 'Đóng',
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      )
      .copyWith(
        button: VpsButton.primaryDangerXsSmall(
          title: VPCommonLocalize.current.confirm,
          onPressed: () {
            Navigator.of(context).pop();
            onConfirmCallback();
          },
        ),
      )
      .showDialog(context);
}

Widget _buildInfoRow(
  BuildContext context, {
  required String label,
  required String value,
  Color? valueColor,
}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Text(
        label,
        style: context.textStyle.body14?.copyWith(color: vpColor.textSecondary),
      ),
      Text(
        value,
        style: context.textStyle.subtitle14?.copyWith(
          color: valueColor ?? vpColor.textPrimary,
          fontWeight: FontWeight.w600,
        ),
      ),
    ],
  );
}

String _getPriceDisplayValue(OrderBookModel item) {
  if (item.priceType == 'LO') {
    return item.price ?? '-';
  } else {
    return item.priceType ?? '-';
  }
}
