import 'package:flutter/material.dart';
import 'package:vp_common/widget/vp_flashing_color_view.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/holding_portfolio/table/holding_portfolio_table_cubit.dart';
import 'package:vp_trading/cubit/holding_portfolio/table/holding_portfolio_table_state.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';

class PortfolioProfitLossInfo extends StatelessWidget {
  final HoldingPortfolioStockModel item;

  const PortfolioProfitLossInfo({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      HoldingPortfolioTableCubit,
      HoldingPortfolioTableState,
      ProfitLossDisplayMode
    >(
      selector: (state) => state.profitLossDisplayMode,
      builder: (context, profitLossDisplayMode) {
        final isAbsoluteMode =
            profitLossDisplayMode == ProfitLossDisplayMode.absolute;

        return VPFlashingColorView(
          key: ValueKey(item.symbol),
          data: item,
          flashColor: item.colorStock ?? Colors.red,
          builder: (child, status) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildPrimaryRow(context, isAbsoluteMode, status),
                if (isAbsoluteMode) _buildSecondaryRow(context, status),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildPrimaryRow(
    BuildContext context,
    bool isAbsoluteMode,
    FlashingStatus? status,
  ) {
    final text =
        isAbsoluteMode
            ? "${item.pnlRateDirectionSymbol} ${item.pnlView ?? '-'}"
            : "${item.pnlRateDirectionSymbol} ${item.pnlRateView ?? '-'}";

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!isAbsoluteMode) _buildPnlDirectionSymbol(),
        Flexible(
          child: _buildStyledText(
            context,
            text,
            (status == FlashingStatus.start)
                ? context.textStyle.subtitle14?.copyWith(
                  color: vpColor.textWhite,
                )
                : context.textStyle.subtitle14?.copyWith(color: item.colorPnl),
          ),
        ),
      ],
    );
  }

  Widget _buildSecondaryRow(BuildContext context, FlashingStatus? status) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildPnlDirectionSymbol(),
        _buildStyledText(
          context,
          item.pnlRateView ?? '-',
          (status == FlashingStatus.start)
              ? context.textStyle.body14?.copyWith(color: vpColor.textWhite)
              : context.textStyle.body14?.copyWith(color: item.colorPnl),
        ),
      ],
    );
  }

  Widget _buildStyledText(
    BuildContext context,
    String text,
    TextStyle? baseStyle,
  ) {
    return AutoSizeText(
      text,
      style: baseStyle,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      minFontSize: 10,
    );
  }

  Widget _buildPnlDirectionSymbol() {
    if (item.pnlView == null || item.pnlValue == 0) {
      return const SizedBox.shrink();
    }

    return Icon(
      item.pnlValue > 0 ? Icons.arrow_drop_up : Icons.arrow_drop_down,
      color: item.colorPnl,
      size: 24,
    );
  }
}
