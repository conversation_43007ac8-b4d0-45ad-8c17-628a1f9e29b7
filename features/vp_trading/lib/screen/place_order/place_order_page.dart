import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/loading_builder.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/order_suggest/order_suggest_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/cubit/place_order/submit/place_order_submit_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/bottom/order_bottom_layout.dart';
import 'package:vp_trading/screen/place_order/widgets/place_order_view.dart';

enum PlaceOrderType { normal, derivative }

extension PlaceOrderTypeD on PlaceOrderType {
  String get title => switch (this) {
    PlaceOrderType.normal => "Cơ sở",
    PlaceOrderType.derivative => "Phái sinh",
  };
}

class PlaceOrderPage extends StatelessWidget {
  const PlaceOrderPage({required this.argument, super.key});

  final PlaceOrderArgs argument;

  @override
  Widget build(BuildContext context) {
    final subAccountType =
        argument.subAccountType ??
        GetIt.instance<SubAccountCubit>().defaultSubAccount.toSubAccountType;
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create:
              (_) => PlaceOrderCubit(
                symbol: argument.symbol,
                action: argument.action,
                subAccountType: subAccountType,
              )..initState(),
        ),
        BlocProvider(
          create: (_) => StockInfoCubit()..loadData(argument.symbol),
        ),

        BlocProvider(create: (_) => OrderSuggestCubit()),
        BlocProvider(create: (_) => PlaceOrderSubmitCubit()),

        BlocProvider(create: (_) => ValidateOrderCubit()),
        BlocProvider(
          create:
              (_) =>
                  AvailableTradeCubit()..getAvailableTrade(
                    accountId: subAccountType.toSubAccountModel()?.id ?? "",
                    symbol: argument.symbol,
                  ),
        ),
      ],

      child: MultiBlocListener(
        listeners: [
          BlocListener<StockInfoCubit, StockInfoState>(
            listenWhen:
                (previous, current) =>
                    previous.stockInfo == null && current.stockInfo != null,
            listener: (context, state) {
              context.read<ValidateOrderCubit>().updateParam(
                action: argument.action,
              );
              if (argument.price != null) {
                if (argument.price! > 0) {
                  context.read<PlaceOrderCubit>().updatePrice(
                    argument.price!.getPriceFormatted(convertToThousand: true),
                  );
                }
              } else {
                // if (state.stockInfo!.closePrice! > 0 ||
                //     state.stockInfo!.refPrice! > 0) {
                //   context.read<PlaceOrderCubit>().updatePrice(
                //     (state.stockInfo!.closePrice! > 0
                //             ? state.stockInfo!.closePrice!
                //             : state.stockInfo!.refPrice!)
                //         .getPriceFormatted(convertToThousand: true),
                //   );
                // }
              }
              Future.delayed(const Duration(milliseconds: 200), () {
                if (context.mounted) {
                  context.read<StockInfoCubit>().onRealtimeChanged(true);
                }
              });
            },
          ),
          BlocListener<StockInfoCubit, StockInfoState>(
            listenWhen:
                (previous, current) =>
                    previous.stockInfo != null &&
                    previous.stockInfo != current.stockInfo,
            listener: (context, state) {
              context.read<PlaceOrderCubit>().clear();
            },
          ),
          BlocListener<StockInfoCubit, StockInfoState>(
            listenWhen:
                (previous, current) => previous.stockInfo != current.stockInfo,
            listener: (context, state) {
              //when choose buy or sell
              context.read<ValidateOrderCubit>().updateParam(
                stockInfo: state.stockInfo,
              );
              if (state.isRealtime) {
                if (state.stockInfo!.closePrice! > 0 ||
                    state.stockInfo!.refPrice! > 0) {
                  context.read<StockInfoCubit>().updatePrice(
                    (state.stockInfo!.closePrice! > 0
                            ? state.stockInfo!.closePrice!
                            : state.stockInfo!.refPrice!)
                        .getPriceFormatted(convertToThousand: true),
                  );
                }
              }
              context.read<ValidateOrderCubit>().clear();
              context.read<OrderSuggestCubit>().stockInfo = state.stockInfo;
              context.read<OrderSuggestCubit>().getMarketStatus();
            },
          ),
          BlocListener<PlaceOrderCubit, PlaceOrderState>(
            listenWhen:
                (previous, current) =>
                    previous.action != current.action ||
                    previous.orderType != current.orderType,
            listener: (context, state) {
              context.read<ValidateOrderCubit>().updateParam(
                action: state.action,
                orderType: state.orderType,
              );
            },
          ),

          BlocListener<PlaceOrderCubit, PlaceOrderState>(
            listenWhen:
                (previous, current) => previous.action != current.action,
            listener: (context, state) {
              //when choose buy or sell
              // context.read<ValidateOrderCubit>().clear();
              // context.read<PlaceOrderCubit>().clear();
              context.read<AvailableTradeCubit>().getAvailableTrade(
                accountId: state.subAccountType.toSubAccountModel()?.id ?? "",
                symbol: state.symbol,
                quotePrice:
                    context
                        .read<ValidateOrderCubit>()
                        .state
                        .currentPrice
                        ?.price
                        .toString(),
              );
            },
          ),

          BlocListener<PlaceOrderCubit, PlaceOrderState>(
            listenWhen:
                (previous, current) => previous.orderType != current.orderType,
            listener: (context, state) {
              context.read<OrderSuggestCubit>().orderType = state.orderType;
              context.read<OrderSuggestCubit>().updateSuggest();
            },
          ),

          BlocListener<PlaceOrderCubit, PlaceOrderState>(
            listenWhen:
                (previous, current) =>
                    previous.subAccountType != current.subAccountType,
            listener: (context, state) {
              context.read<AvailableTradeCubit>().getAvailableTrade(
                accountId: state.subAccountType.toSubAccountModel()?.id ?? "",
                symbol: state.symbol,
              );
              context.read<StockInfoCubit>().onOrderLotTypeChanged(
                state.orderLotType,
              );
              context.read<StockInfoCubit>().loadData(state.symbol);
            },
          ),

          BlocListener<PlaceOrderCubit, PlaceOrderState>(
            listenWhen:
                (previous, current) =>
                    previous.symbol != current.symbol ||
                    previous.orderLotType != current.orderLotType,
            listener: (context, state) {
              context.read<AvailableTradeCubit>().getAvailableTrade(
                accountId: state.subAccountType.toSubAccountModel()?.id ?? "",
                symbol: state.symbol,
              );
              context.read<StockInfoCubit>().onOrderLotTypeChanged(
                state.orderLotType,
              );
              context.read<StockInfoCubit>().loadData(state.symbol);
              context.read<ValidateOrderCubit>().clear();
              context.read<PlaceOrderCubit>().clear();
            },
          ),
          BlocListener<AvailableTradeCubit, AvailableTradeState>(
            listenWhen:
                (previous, current) =>
                    previous.availableTrade != current.availableTrade,
            listener: (context, state) {
              context.read<ValidateOrderCubit>().updateParam(
                availableTrade: state.availableTrade,
              );
            },
          ),
          BlocListener<PlaceOrderSubmitCubit, PlaceOrderSubmitState>(
            listener: (context, state) {
              if (state.status.isSuccess) {
                context.read<AvailableTradeCubit>().getAvailableTrade(
                  accountId:
                      context
                          .read<PlaceOrderCubit>()
                          .state
                          .subAccountType
                          .toSubAccountModel()
                          ?.id ??
                      "",
                  symbol: context.read<PlaceOrderCubit>().state.symbol,
                );
                context.showSuccess(content: "Yêu cầu đặt lệnh thành công");
                context.read<PlaceOrderCubit>().saveCacheSymbolSearch();
                if (!context.read<PlaceOrderCubit>().state.isSaveCommand) {
                  context.read<ValidateOrderCubit>().clear();
                }
              }
            },
          ),
        ],
        child: BlocBuilder<PlaceOrderSubmitCubit, PlaceOrderSubmitState>(
          builder: (context, state) {
            return VPLoadingBuilder(
              showLoading: state.status.isLoading,
              builder: (context, child) => child!,
              child: const Column(
                children: [
                  Expanded(child: PlaceOrderView()),
                  OrderBottomLayout(),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
