part of 'derivatives_order_book_multi_select_cubit.dart';

/// State for derivatives order book multi-select functionality
final class DerivativesOrderBookMultiSelectState extends Equatable {
 
  final bool isMultiSelectMode;
  
  final Set<String> selectedOrderIds;
  
  final bool isCancelling;
  
  final String? errorMessage;
  
  final bool isSuccess;
  
  final String? successMessage;

  const DerivativesOrderBookMultiSelectState({
    this.isMultiSelectMode = false,
    this.selectedOrderIds = const <String>{},
    this.isCancelling = false,
    this.errorMessage,
    this.isSuccess = false,
    this.successMessage,
  });

  @override
  List<Object?> get props => [
    isMultiSelectMode,
    selectedOrderIds,
    isCancelling,
    errorMessage,
    isSuccess,
    successMessage,
  ];

  DerivativesOrderBookMultiSelectState copyWith({
    bool? isMultiSelectMode,
    Set<String>? selectedOrderIds,
    bool? isCancelling,
    String? errorMessage,
    bool? isSuccess,
    String? successMessage,
  }) {
    return DerivativesOrderBookMultiSelectState(
      isMultiSelectMode: isMultiSelectMode ?? this.isMultiSelectMode,
      selectedOrderIds: selectedOrderIds ?? this.selectedOrderIds,
      isCancelling: isCancelling ?? this.isCancelling,
      errorMessage: errorMessage,
      isSuccess: isSuccess ?? this.isSuccess,
      successMessage: successMessage,
    );
  }

  /// Check if any orders are selected
  bool get hasSelectedOrders => selectedOrderIds.isNotEmpty;

  /// Get count of selected orders
  int get selectedCount => selectedOrderIds.length;

  /// Check if order is selected
  bool isOrderSelected(String orderId) => selectedOrderIds.contains(orderId);
}
