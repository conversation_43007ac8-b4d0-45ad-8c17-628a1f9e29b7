import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class TooltipBehaviorWidget extends StatelessWidget {
  const TooltipBehaviorWidget({
    Key? key,
    this.title = '',
    this.content = '',
    this.subTitle,
    this.titleStyle,
    this.contentStyle,
    this.subTitleStyle,
  }) : super(key: key);
  final String title;
  final String content;
  final String? subTitle;
  final TextStyle? titleStyle;
  final TextStyle? contentStyle;
  final TextStyle? subTitleStyle;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: themeData.white,
        border: Border.all(color: themeData.highlightBg, width: 1),
        boxShadow: [
          BoxShadow(
            color: themeData.boxShadow.withValues(alpha: 0.2),
            spreadRadius: 2,
            blurRadius: 2,
            offset: const Offset(0, 0.75),
          ),
        ],
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style:
                titleStyle ??
                vpTextStyle.subtitle14.copyColor(themeData.gray900)
               
          ),
          Visibility(
            visible: subTitle != null,
            child: Text(
              (subTitle ?? '').replaceAll('\n', ''),
              style:
                  subTitleStyle ??
                  vpTextStyle.captionMedium.copyColor(themeData.primary),
            ),
          ),
          Text(
            content,
            // NumberFormat.compact().format(value).toString(),
            style:
                contentStyle ??
                vpTextStyle.captionRegular.copyColor(themeData.red),
          ),
        ],
      ),
    );
  }
}
