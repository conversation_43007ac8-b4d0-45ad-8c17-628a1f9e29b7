import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:vp_assets/model/asset_report/chart_data_report_model.dart';
import 'package:vp_assets/widgets/app_calendar_hint_widget.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';

class InvestmentPerformanceWidget extends StatefulWidget {
  const InvestmentPerformanceWidget({
    super.key,
    this.listCategoryGrowth,
    this.listPerfomanceVNINDEX,
    required this.startDate,
    required this.endDate,
    this.resetStartDate,
    this.resetEndDate,
    this.maxDate,
  });

  final List<ChartAssetReportModel>? listCategoryGrowth;
  final List<ChartAssetReportModel>? listPerfomanceVNINDEX;

  final DateTime startDate;

  final DateTime endDate;

  final DateTime? resetStartDate;

  final DateTime? resetEndDate;

  final DateTime? maxDate;

  @override
  State<InvestmentPerformanceWidget> createState() =>
      _InvestmentPerformanceWidgetState();
}

class _InvestmentPerformanceWidgetState
    extends State<InvestmentPerformanceWidget> {
  late DateTime stateDate = widget.startDate;
  late DateTime endDate = widget.endDate;

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height / 5;

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              /// build start date picker
              Expanded(
                child: CalendarWidget(
                  onTap: () => showCalendar(),
                  text: stateDate.toDate(),
                ),
              ),

              const SizedBox(width: 8),

              Text(
                '-',
               style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
              ),

              const SizedBox(width: 8),

              /// build end date picker
              Expanded(
                child: CalendarWidget(
                  onTap: () => showCalendar(),
                  text: endDate.toDate(),
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          width: width,
          height: height * 2,
          child: SfCartesianChart(
            primaryXAxis: CategoryAxis(
              edgeLabelPlacement: EdgeLabelPlacement.shift,
              majorTickLines: MajorTickLines(
                width: 0, // Độ rộng của các đường gạch chia
                color: Colors.black, // Màu sắc của các đường gạch chia
              ),
              majorGridLines: MajorGridLines(width: 0), // Ẩn gridlines trục X
            ),
            primaryYAxis: NumericAxis(
              minimum: 0,
              maximum: 100,
              majorGridLines: MajorGridLines(width: 1),
              // Tắt lưới kẻ chính
              minorGridLines: MinorGridLines(width: 1),
              majorTickLines: MajorTickLines(
                width: 0, // Độ rộng của các đường gạch chia
                color: Colors.black, // Màu sắc của các đường gạch chia
              ),
              // Tắt lưới kẻ phụ
              labelFormat: '{value}%',
            ),
            legend: Legend(
              isVisible: true,
              position: LegendPosition.bottom, // Vị trí của chú thích
              overflowMode:
                  LegendItemOverflowMode.wrap, // Chế độ tràn khi quá nhiều mục
            ),
            series: <CartesianSeries>[
              SplineSeries<ChartAssetReportModel, String>(
                dataSource: widget.listCategoryGrowth,
                xValueMapper: (ChartAssetReportModel data, _) => data.x,
                yValueMapper: (ChartAssetReportModel data, _) => data.y,
                name: 'Tăng trưởng danh mục',
                pointColorMapper:
                    (ChartAssetReportModel data, _) => Colors.blue,
                legendIconType: LegendIconType.circle,
                color: Colors.blue,
              ),
              SplineSeries<ChartAssetReportModel, String>(
                dataSource: widget.listPerfomanceVNINDEX,
                xValueMapper: (ChartAssetReportModel data, _) => data.x,
                yValueMapper: (ChartAssetReportModel data, _) => data.y,
                name: 'Tăng trưởng VNINDEX',
                pointColorMapper:
                    (ChartAssetReportModel data, _) => Colors.yellow,
                legendIconType: LegendIconType.circle,
                color: Colors.yellow,
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  void showCalendar() async {
    final data = await showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return BondBottomSheet(
          padding: const EdgeInsets.only(top: 16, bottom: 28),
          initialChildSize:
              MediaQuery.of(context).size.height -
              MediaQuery.of(context).padding.top -
              kToolbarHeight,
          child: AppCustomRangeCalendar(
            resetStartDay: widget.resetStartDate,
            resetEndDay: widget.resetEndDate,
            startDay: stateDate,
            endDay: endDate,
            maxDay: widget.maxDate,
            minDay:
                TimeUtils().now.outOfSession() || TimeUtils().now.inBreakTime()
                    ? widget.maxDate?.changeMonth(-36)
                    : widget.maxDate?.changeMonth(-12),
            dateRange: 90,
          ),
        );
      },
    );

    if (data is List<DateTime?> && data.length == 2) {
      stateDate = data.first!;
      endDate = data.last!;
      setState(() {
        widget.bloc?.getInvestmentEfficiency();
      });
    }
  }

  @override
  void initState() {
    super.initState();
  }
}
