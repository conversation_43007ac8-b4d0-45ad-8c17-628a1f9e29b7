import 'package:flutter/material.dart';
import 'loading_state.dart';

class LoadingBuilder extends StatelessWidget {
  final LoadingState bloc;
  final Widget child;
  final Widget loading;
  final Widget? retry;
  final bool stackLoading;

  const LoadingBuilder(
    this.bloc, {
    super.key,
    required this.child,
    required this.loading,
    this.retry,
    this.stackLoading = true,
  });

  @override
  Widget build(BuildContext context) => ValueListenableBuilder<Set>(
      valueListenable: bloc.loadingListenable,
      builder: (_, __, ___) => Stack(
            children: [
              stackLoading || !bloc.isLoading
                  ? bloc.isRetry && retry != null
                      ? retry!
                      : child
                  : const Positioned.fill(child: SizedBox.shrink()),
              if (bloc.isLoading)
                Positioned.fill(child: AbsorbPointer(child: loading))
            ],
          ));
}
