import 'package:flutter/material.dart';
import 'package:tuple/tuple.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_utility/model/mixin/paging_state.dart';

mixin LoadingState {
  static const _retry = '_LoadingRetryState';

  late final ValueNotifier<Set> loadingListenable = ValueNotifier<Set>({});

  Set get _loadingStack => loadingListenable.value;

  bool get isLoading => _loadingStack.any((e) => e != _retry);

  bool get isRetry => _loadingStack.contains(_retry);

  _add(value) => loadingListenable.value = {..._loadingStack, value};

  _remove(value) => loadingListenable.value = {..._loadingStack}..remove(value);

  Future call<T>(
    Future Function() future, {
    bool loading = true,
    bool retry = false,
    Function(T result)? onDone,
    required void Function(dynamic e) onError,
  }) async {
    if (loading) _add(future.hashCode);
    try {
      final result = await future();
      if (isRetry) _remove(_retry);
      return onDone?.call(result);
    } catch (e) {
      onError(e);
      if (retry) _add(_retry);
    } finally {
      if (loading) _remove(future.hashCode);
    }
  }
}

extension LoadingStateExt on LoadingState {
  Future callApi<T>(
      Future Function() future, {
        bool loading = true,
        bool retry = false,
        Function(T result)? onDone,
        void Function(dynamic e) onError = showError,
      }) =>
      call<T>(future,
          loading: loading, retry: retry, onDone: onDone, onError: onError);

  Future callPaging<T>(
      Future<Tuple3<List<T>, int, int>> Function(int pageOffset) callData, {
        required Function(List<T> data) onData,
        int initOffset = 1,
        void Function(dynamic e) onError = showError,
      }) {
    assert(this is PagingState<T>);
    return call(
            () => (this as PagingState<T>).paging(
          callData,
          onData: onData,
          initOffset: initOffset,
        ),
        retry: true,
        onError: onError);
  }

  Future loadMore() {
    assert(this is PagingState);
    return call((this as PagingState).more,
        retry: true, loading: false, onError: showError);
  }
}


