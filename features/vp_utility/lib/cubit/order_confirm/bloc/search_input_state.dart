import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/assets.gen.dart';
import 'package:vp_utility/utils/input_field_state.dart';

class SearchInputState extends InputFieldState {
  @override
  Widget? prefix(Color color) => Padding(
    padding: const EdgeInsets.only(left: 8),
    child: SvgPicture.asset(
      VpUtilityAssets.icons.icSearchNew.path,
      package: VpUtilityAssets.package,
      colorFilter: ColorFilter.mode(themeData.gray900, BlendMode.srcIn),
      width: 24,
    ),
  );

  @override
  bool get clear => true;

  @override
  String get hintText => 'Tìm kiếm mã cổ phiếu';

  @override
  TextAlign get textAlign => TextAlign.start;

  @override
  List<TextInputFormatter> get inputFormatters => [trim];

  @override
  EdgeInsets get contentPadding => const EdgeInsets.all(8).copyWith(right: 16);
}
