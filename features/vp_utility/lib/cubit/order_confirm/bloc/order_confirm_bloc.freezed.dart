// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_confirm_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OrderConfirmState {
  bool get showMultiSelect => throw _privateConstructorUsedError;
  List<OrderConfirmObj> get listOderConfirm =>
      throw _privateConstructorUsedError;
  String get symbol => throw _privateConstructorUsedError;
  SubAccountType get subAccountType => throw _privateConstructorUsedError;
  OrderStatus get orderStatus => throw _privateConstructorUsedError;
  DateTime get fromDate => throw _privateConstructorUsedError;
  DateTime get toDate => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $OrderConfirmStateCopyWith<OrderConfirmState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderConfirmStateCopyWith<$Res> {
  factory $OrderConfirmStateCopyWith(
      OrderConfirmState value, $Res Function(OrderConfirmState) then) =
  _$OrderConfirmStateCopyWithImpl<$Res, OrderConfirmState>;
  @useResult
  $Res call(
      {bool showMultiSelect,
        List<OrderConfirmObj> listOderConfirm,
        String symbol,
        SubAccountType subAccountType,
        OrderStatus orderStatus,
        DateTime fromDate,
        DateTime toDate});
}

/// @nodoc
class _$OrderConfirmStateCopyWithImpl<$Res, $Val extends OrderConfirmState>
    implements $OrderConfirmStateCopyWith<$Res> {
  _$OrderConfirmStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showMultiSelect = null,
    Object? listOderConfirm = null,
    Object? symbol = null,
    Object? subAccountType = null,
    Object? orderStatus = null,
    Object? fromDate = null,
    Object? toDate = null,
  }) {
    return _then(_value.copyWith(
      showMultiSelect: null == showMultiSelect
          ? _value.showMultiSelect
          : showMultiSelect // ignore: cast_nullable_to_non_nullable
      as bool,
      listOderConfirm: null == listOderConfirm
          ? _value.listOderConfirm
          : listOderConfirm // ignore: cast_nullable_to_non_nullable
      as List<OrderConfirmObj>,
      symbol: null == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
      as String,
      subAccountType: null == subAccountType
          ? _value.subAccountType
          : subAccountType // ignore: cast_nullable_to_non_nullable
      as SubAccountType,
      orderStatus: null == orderStatus
          ? _value.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
      as OrderStatus,
      fromDate: null == fromDate
          ? _value.fromDate
          : fromDate // ignore: cast_nullable_to_non_nullable
      as DateTime,
      toDate: null == toDate
          ? _value.toDate
          : toDate // ignore: cast_nullable_to_non_nullable
      as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderConfirmStateImplCopyWith<$Res>
    implements $OrderConfirmStateCopyWith<$Res> {
  factory _$$OrderConfirmStateImplCopyWith(_$OrderConfirmStateImpl value,
      $Res Function(_$OrderConfirmStateImpl) then) =
  __$$OrderConfirmStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool showMultiSelect,
        List<OrderConfirmObj> listOderConfirm,
        String symbol,
        SubAccountType subAccountType,
        OrderStatus orderStatus,
        DateTime fromDate,
        DateTime toDate});
}

/// @nodoc
class __$$OrderConfirmStateImplCopyWithImpl<$Res>
    extends _$OrderConfirmStateCopyWithImpl<$Res, _$OrderConfirmStateImpl>
    implements _$$OrderConfirmStateImplCopyWith<$Res> {
  __$$OrderConfirmStateImplCopyWithImpl(_$OrderConfirmStateImpl _value,
      $Res Function(_$OrderConfirmStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showMultiSelect = null,
    Object? listOderConfirm = null,
    Object? symbol = null,
    Object? subAccountType = null,
    Object? orderStatus = null,
    Object? fromDate = null,
    Object? toDate = null,
  }) {
    return _then(_$OrderConfirmStateImpl(
      showMultiSelect: null == showMultiSelect
          ? _value.showMultiSelect
          : showMultiSelect // ignore: cast_nullable_to_non_nullable
      as bool,
      listOderConfirm: null == listOderConfirm
          ? _value._listOderConfirm
          : listOderConfirm // ignore: cast_nullable_to_non_nullable
      as List<OrderConfirmObj>,
      symbol: null == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
      as String,
      subAccountType: null == subAccountType
          ? _value.subAccountType
          : subAccountType // ignore: cast_nullable_to_non_nullable
      as SubAccountType,
      orderStatus: null == orderStatus
          ? _value.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
      as OrderStatus,
      fromDate: null == fromDate
          ? _value.fromDate
          : fromDate // ignore: cast_nullable_to_non_nullable
      as DateTime,
      toDate: null == toDate
          ? _value.toDate
          : toDate // ignore: cast_nullable_to_non_nullable
      as DateTime,
    ));
  }
}

/// @nodoc

class _$OrderConfirmStateImpl extends _OrderConfirmState {
  const _$OrderConfirmStateImpl(
      {this.showMultiSelect = false,
        final List<OrderConfirmObj> listOderConfirm = const [],
        this.symbol = '',
        this.subAccountType = SubAccountType.normal,
        this.orderStatus = OrderStatus.all,
        required this.fromDate,
        required this.toDate})
      : _listOderConfirm = listOderConfirm,
        super._();

  @override
  @JsonKey()
  final bool showMultiSelect;
  final List<OrderConfirmObj> _listOderConfirm;
  @override
  @JsonKey()
  List<OrderConfirmObj> get listOderConfirm {
    if (_listOderConfirm is EqualUnmodifiableListView) return _listOderConfirm;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_listOderConfirm);
  }

  @override
  @JsonKey()
  final String symbol;
  @override
  @JsonKey()
  final SubAccountType subAccountType;
  @override
  @JsonKey()
  final OrderStatus orderStatus;
  @override
  final DateTime fromDate;
  @override
  final DateTime toDate;

  @override
  String toString() {
    return 'OrderConfirmState(showMultiSelect: $showMultiSelect, listOderConfirm: $listOderConfirm, symbol: $symbol, subAccountType: $subAccountType, orderStatus: $orderStatus, fromDate: $fromDate, toDate: $toDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderConfirmStateImpl &&
            (identical(other.showMultiSelect, showMultiSelect) ||
                other.showMultiSelect == showMultiSelect) &&
            const DeepCollectionEquality()
                .equals(other._listOderConfirm, _listOderConfirm) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.subAccountType, subAccountType) ||
                other.subAccountType == subAccountType) &&
            (identical(other.orderStatus, orderStatus) ||
                other.orderStatus == orderStatus) &&
            (identical(other.fromDate, fromDate) ||
                other.fromDate == fromDate) &&
            (identical(other.toDate, toDate) || other.toDate == toDate));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      showMultiSelect,
      const DeepCollectionEquality().hash(_listOderConfirm),
      symbol,
      subAccountType,
      orderStatus,
      fromDate,
      toDate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderConfirmStateImplCopyWith<_$OrderConfirmStateImpl> get copyWith =>
      __$$OrderConfirmStateImplCopyWithImpl<_$OrderConfirmStateImpl>(
          this, _$identity);
}

abstract class _OrderConfirmState extends OrderConfirmState {
  const factory _OrderConfirmState(
      {final bool showMultiSelect,
        final List<OrderConfirmObj> listOderConfirm,
        final String symbol,
        final SubAccountType subAccountType,
        final OrderStatus orderStatus,
        required final DateTime fromDate,
        required final DateTime toDate}) = _$OrderConfirmStateImpl;
  const _OrderConfirmState._() : super._();

  @override
  bool get showMultiSelect;
  @override
  List<OrderConfirmObj> get listOderConfirm;
  @override
  String get symbol;
  @override
  SubAccountType get subAccountType;
  @override
  OrderStatus get orderStatus;
  @override
  DateTime get fromDate;
  @override
  DateTime get toDate;
  @override
  @JsonKey(ignore: true)
  _$$OrderConfirmStateImplCopyWith<_$OrderConfirmStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
