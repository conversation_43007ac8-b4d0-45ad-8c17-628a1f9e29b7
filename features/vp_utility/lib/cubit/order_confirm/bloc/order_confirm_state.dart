part of 'order_confirm_bloc.dart';

@freezed
class OrderConfirmState with _$OrderConfirmState {
  const OrderConfirmState._();

  const factory OrderConfirmState({
    @Default(false) bool showMultiSelect,
    @Default([]) List<OrderConfirmObj> listOderConfirm,
    @Default('') String symbol,
    @Default(SubAccountType.normal) SubAccountType subAccountType,
    @Default(OrderStatus.all) OrderStatus orderStatus,
    required DateTime fromDate,
    required DateTime toDate,
  }) = _OrderConfirmState;
}
