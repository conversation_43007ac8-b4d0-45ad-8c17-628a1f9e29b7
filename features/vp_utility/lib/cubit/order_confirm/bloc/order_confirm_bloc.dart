import 'dart:async';

import 'package:vp_common/vp_common.dart';
import 'package:vp_common/widget/log_api/models/app_base_response.dart';
import 'package:vp_core/vp_core.dart';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vp_utility/core/repository/order_confirm/order_confirm_repository.dart';
import 'package:vp_utility/model/mixin/debounce.dart';
import 'package:vp_utility/cubit/loading/loading_state.dart';
import 'package:vp_utility/model/mixin/message/message.dart';
import 'package:vp_utility/model/mixin/paging_state.dart' as ps;
import 'package:vp_utility/model/response/check_model.dart';
import 'package:vp_utility/model/response/order_confirm/enum/order_status.dart';
import 'package:vp_utility/model/response/order_confirm/models/order_confirm_obj.dart';
import 'package:vp_utility/model/response/order_confirm/models/order_confirm_request_obj.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_checkbox.dart';

import 'search_input_state.dart';

part 'order_confirm_bloc.freezed.dart';

part 'order_confirm_state.dart';

class OrderConfirmBloc extends Cubit<OrderConfirmState>
    with MessageState, LoadingState, ps.PagingState<OrderConfirmObj>, Debounce {
  late final searchInputState = SearchInputState();

  OrderConfirmBloc()
    : super(
        OrderConfirmState(
          fromDate: DateTime(DateTime.now().year, 1, 1),
          toDate: DateTime.now(),
        ),
      ) {
    searchInputState.addListener(
      text: (text) => emit(state.copyWith(symbol: text)),
    );
  }

  String? symbolExpanded;

  StreamSubscription? socketAccountSubscription;

  @override
  void onChange(Change<OrderConfirmState> change) {
    super.onChange(change);
    if (change.currentState.symbol != change.nextState.symbol) {
      debounce(callAPIOrderConfirm);
    }
  }

  void init() {
    callAPIOrderConfirm();
    listenSocketAccount();
  }

  Future onRefresh() => callAPIOrderConfirm();

  void listenSocketAccount() {
    // socketAccountSubscription = SocketAccountConnect().orderStream.listen(
    //   (_) => onRefresh(),
    // );
  }

  //-> Chuyen sang multil Select
  void onChangeMultiSelect() {
    setIndexSelect.clear();
    emit(state.copyWith(showMultiSelect: !state.showMultiSelect));
  }

  void onSubAccountTypeChanged(SubAccountType type) {
    emit(state.copyWith(subAccountType: type));
    callAPIOrderConfirm();
  }

  void onOrderStatusChanged(OrderStatus orderStatus) {
    emit(state.copyWith(orderStatus: orderStatus));
    callAPIOrderConfirm();
  }

  void onDateChanged(DateTime fromDate, DateTime toDate) {
    emit(state.copyWith(fromDate: fromDate, toDate: toDate));
    callAPIOrderConfirm();
  }

  Future callAPIOrderConfirm() => callPaging<OrderConfirmObj>(
    (pageOffset) => GetIt.instance<OrderConfirmRepository>().confirmOrders(
      subAccount: state.subAccountType.toSubAccountModel()?.id ?? '',
      fromDate: state.fromDate.formatToDdMmYyyy(),
      toDate: state.toDate.formatToDdMmYyyy(),
      execType: state.orderStatus.api,
      symbol: state.symbol.toUpperCase(),
      pageIndex: pageOffset,
      pageSize: 20,
    ),
    onData: (result) => emit(state.copyWith(listOderConfirm: result)),
  );

  //-> Call API kiem tra thong tin xac nhan lenh
  Future<void> checkConfirmOrders(String orderID) => callApi<CheckModel>(
    () => orderConfirmRepository.checkConfirmOrders(
      subAccount: state.subAccountType.toSubAccountModel()?.id,
      orderID: orderID,
    ),
    onDone: (result) {
      callApi<AppBaseResponse>(
        () {
          final param = OrderConfirmRequestObj(
            authType: result.authtype,
            transactionId: result.transactionId,
            tokenid: result.tokenid,
            orderId: orderID,
          );
          return orderConfirmRepository.transConfirmOrders(
            subAccount: state.subAccountType.toSubAccountModel()?.id,
            param: param,
          );
        },
        onDone: (value) {
          if (value.isSuccess()) {
            send(
              Message(
                type: 'orderConfirmSuccess',
                content: state.showMultiSelect,
              ),
            );
          }
        },
      );
    },
  );

  Future<void> checkConfirmAllOrders() => callApi<CheckModel>(
    () => orderConfirmRepository.checkConfirmAllOrders(
      subAccount: state.subAccountType.toSubAccountModel()?.id,
    ),
    onDone: (result) {
      callApi<AppBaseResponse>(
        () {
          final param = OrderConfirmRequestObj(
            authType: result.authtype,
            transactionId: result.transactionId,
            tokenid: result.tokenid,
          );
          return orderConfirmRepository.transConfirmAllOrders(
            subAccount: state.subAccountType.toSubAccountModel()?.id,
            param: param,
          );
        },
        onDone: (value) {
          if (value.isSuccess()) {
            send(
              Message(
                type: 'orderConfirmSuccess',
                content: state.showMultiSelect,
              ),
            );
          }
        },
      );
    },
  );

  OrderConfirmRepository get orderConfirmRepository =>
      GetIt.instance<OrderConfirmRepository>();

  //-> Xu ly mutil select
  Set<int> setIndexSelect = {};

  OrderConfirmCheckBoxStatus get selectAllStatus {
    if (setIndexSelect.isEmpty) {
      return OrderConfirmCheckBoxStatus.unAllSelected;
    }
    if (setIndexSelect.length < state.listOderConfirm.length) {
      return OrderConfirmCheckBoxStatus.continueAllSelected;
    }
    return OrderConfirmCheckBoxStatus.allSelected;
  }

  void setSelectAll(bool selectAll) {
    if (selectAll) {
      for (int i = 0; i < state.listOderConfirm.length; i++) {
        setIndexSelect.add(i);
      }
    } else {
      setIndexSelect.clear();
    }
  }

  void insertToSetIndexSelect(int index, bool isSelect) =>
      isSelect ? setIndexSelect.add(index) : setIndexSelect.remove(index);

  String getOrderIDMutilSelect() {
    String orderID = '';

    if (setIndexSelect.isEmpty) return orderID;

    try {
      for (var index in setIndexSelect) {
        orderID +=
            '${state.listOderConfirm.getElementAt(index)?.orderid ?? ''},';
      }

      orderID = orderID.substring(0, orderID.length - 1);
    } catch (e) {
      dlog(e);
    }

    return orderID;
  }

  // Reset list voi expanded.
  void resetListWithExpanded(int index) {
    final order = state.listOderConfirm.getElementAt(index);

    if (order == null) return;

    if (order.isExpanded) {
      symbolExpanded = null;
      order.isExpanded = false;
      return;
    }

    for (var i = 0; i < state.listOderConfirm.length; i++) {
      state.listOderConfirm.getElementAt(i)?.isExpanded = false;
    }

    symbolExpanded = order.symbol;

    order.isExpanded = true;
  }

  @override
  Future<void> close() {
    socketAccountSubscription?.cancel();
    removeDebounce();
    searchInputState.removeListener();
    return super.close();
  }
}
