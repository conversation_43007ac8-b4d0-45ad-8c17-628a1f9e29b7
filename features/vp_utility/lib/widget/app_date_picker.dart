import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/assets.gen.dart';
import 'package:vp_utility/widget/app_custom_range_calendar.dart';

class AppDatePicker extends StatefulWidget {
  final DateTime startDate;
  final DateTime endDate;
  final DateTime? minDay;
  final DateTime? maxDay;
  final DateTime? resetStartDay;
  final DateTime? resetEndDay;
  final int dateRange;
  final Function(DateTime startDate, DateTime endDate) onDateChanged;

  const AppDatePicker({
    super.key,
    required this.startDate,
    required this.endDate,
    required this.onDateChanged,
    this.minDay,
    this.maxDay,
    this.resetStartDay,
    this.resetEndDay,
    this.dateRange = 365 * 3,
  });

  @override
  _AppDatePickerState createState() => _AppDatePickerState();
}

class _AppDatePickerState extends State<AppDatePicker> {
  late DateTime startDate;
  late DateTime endDate;

  @override
  void initState() {
    super.initState();
    startDate = widget.startDate;
    endDate = widget.endDate;
  }

  @override
  Widget build(BuildContext context) => GestureDetector(
    onTap: () =>
        VPPopup.bottomSheet(
              AppCustomRangeCalendar(
                startDay: widget.startDate,
                endDay: widget.endDate,
                minDay: widget.minDay,
                maxDay: widget.maxDay,
                resetStartDay: widget.resetStartDay,
                resetEndDay: widget.resetEndDay,
                dateRange: widget.dateRange,
              ),
            )
            .copyWith(padding: const EdgeInsets.only(top: 20.0))
            .showSheet(context)
            .then(
              (value) =>
                  value != null &&
                      (value as List<DateTime?>).first != null &&
                      value.last != null
                  ? setState(() {
                      startDate = value.first!;
                      endDate = value.last!;
                      widget.onDateChanged(startDate, endDate);
                    })
                  : null,
            ),
    child: Container(
      height: 40,
      decoration: BoxDecoration(
        border: Border.all(color: themeData.borderDisable),
        borderRadius: BorderRadius.circular(8),
        color: vpColor.backgroundElevation0,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          SvgPicture.asset(
            VpUtilityAssets.icons.icDateNew.path,
            package: VpUtilityAssets.package,
            colorFilter: ColorFilter.mode(themeData.gray900, BlendMode.srcIn),
            width: 24,
          ),
         const SizedBox(width: 8,),
          Expanded(
            child: Text(
              '${startDate.formatToDdMmYyyy()} - ${endDate.formatToDdMmYyyy()}',
              style: vpTextStyle.body14?.copyWith(
                color: themeData.black,
                height: 1.1,
              ),
            ),
          ),
        ],
      ),
    ),
  );
}
