import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class OrderConfirmBottomSheet extends StatelessWidget {
  final Widget child;
  final double? initialChildSize;
  final double? maxChildSize;
  final double? minChildSize;
  final EdgeInsets? padding;
  final bool isFullSize;

  const OrderConfirmBottomSheet({
    super.key,
    required this.child,
    this.padding = const EdgeInsets.only(top: 16),
    this.initialChildSize,
    this.isFullSize = false,
    this.maxChildSize,
    this.minChildSize,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: padding,
          height: initialChildSize,
          alignment: Alignment.bottomCenter,
          clipBehavior: Clip.hardEdge,
          decoration: ShapeDecoration(
            color: themeData.bgPopup,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
            ),
          ),
          child: Column(
            children: [
              // kSpacingHeight16,
              Container(
                width: 56,
                height: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: themeData.buttonTopBottomSheet,
                ),
              ),
              initialChildSize != null ? Expanded(child: child) : child,
              if (!isFullSize) SizedBox(height: 32),
            ],
          ),
        ),
      ],
    );
  }
}
