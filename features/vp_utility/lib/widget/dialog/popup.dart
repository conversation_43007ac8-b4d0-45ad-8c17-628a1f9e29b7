import 'package:flutter/material.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/widget/button/button_builder.dart';
import 'package:vp_utility/widget/dialog/mixin/button_layout.dart';
import 'package:vp_utility/widget/dialog/mixin/popup_button.dart';
import 'package:vp_utility/widget/dialog/mixin/popup_icon.dart';

abstract class Popup extends StatelessWidget
    with PopupIcon, PopupButton, ButtonLayout {
  final Widget builder;
  final EdgeInsets iconPadding;
  final double buttonSpacing;
  final double buttonHeight;
  final bool isHorizontalButton;
  final Color? background;

  Popup(
    this.builder, {
    super.key,
    this.iconPadding = const EdgeInsets.only(bottom: 16.0),
    this.buttonSpacing = 8.0,
    this.buttonHeight = 40.0,
    this.isHorizontalButton = true,
    this.background,
  });

  Popup icon(icon) {
    if (popupResource == null) resource = icon;
    return this;
  }

  Popup button(Widget button) {
    startButton == null
        ? start = button
        : endButton == null
        ? end = button
        : null;
    return this;
  }

  Widget? get buttons => layout(
    start: startButton,
    spacing: buttonSpacing,
    buttonHeight: buttonHeight,
    end: endButton,
    isHorizontal: isHorizontalButton,
  );

  Future show(BuildContext context);

  @override
  Widget build(BuildContext context) => builder;
}

extension PopupButtonExtension on Popup {
  Popup get close => button(
    Builder(
      builder:
          (context) => Build.outline(
            label: VpUtilityLocalize.current.oc_close,
            onPressed: Navigator.of(context).pop,
          ),
    ),
  );
}
