import 'package:flutter/material.dart';

mixin ButtonLayout {
  Widget? _single(Widget? start, Widget? end, double buttonHeight) =>
      start != null || end != null
          ? SizedBox(
              width: double.infinity, height: buttonHeight, child: start ?? end)
          : null;

  Widget? layout({
    Widget? start,
    required double spacing,
    required double buttonHeight,
    Widget? end,
    required bool isHorizontal,
  }) =>
      start != null && end != null
          ? isHorizontal
              ? SizedBox(
                  height: buttonHeight,
                  child: Row(
                    children: [
                      Expanded(child: start),
                      SizedBox(width: spacing),
                      Expanded(child: end),
                    ],
                  ),
                )
              : Column(
                  children: [
                    SizedBox(
                        width: double.infinity,
                        height: buttonHeight,
                        child: start),
                    Sized<PERSON>ox(height: spacing),
                    SizedBox(
                        width: double.infinity,
                        height: buttonHeight,
                        child: end),
                  ],
                )
          : _single(start, end, buttonHeight);
}
