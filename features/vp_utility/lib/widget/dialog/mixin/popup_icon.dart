import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

mixin PopupIcon {
  late final dynamic resource;

  dynamic get popupResource {
    try {
      return resource;
    } catch (_) {
      return null;
    }
  }

  Widget popupIcon(EdgeInsets padding) => popupResource is String
      ? Padding(padding: padding, child: SvgPicture.asset(popupResource))
      : popupResource is Widget
          ? Padding(padding: padding, child: popupResource)
          : const SizedBox.shrink();
}
