import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/assets.gen.dart';
import 'package:vp_utility/widget/button/button_widget.dart';

Future showNotifyDialog2({
  required BuildContext context,
  required String title,
  String? content,
  String? image,
  Color? colorButtonRight,
  String? textButtonRight,
  Color? colorBorderButtonRight,
  Color? colorButtonLeft,
  String? textButtonLeft,
  Color? colorBorderButtonLeft,
  void Function(BuildContext context)? onPressedRight,
  void Function(BuildContext context)? onPressedLeft,
  TextStyle? textStyleLeft,
  Widget? contentWidget,
  bool? allowDismiss,
  bool barrierDismissible = false,
  bool showCloseIcon = false,
  double? iconSize,
  EdgeInsets? padding,
  EdgeInsets? imagePadding,
  TextStyle? titleStyle,
}) {
  return showGeneralDialog(
    context: context,
    barrierDismissible: barrierDismissible,
    barrierLabel: '',
    pageBuilder: (context, _, __) {
      return BaseDialog(
        title: title,
        content: content,
        image: image,
        colorButtonRight: colorButtonRight,
        textButtonRight: textButtonRight,
        colorBorderButtonRight: colorBorderButtonRight,
        onPressedRight:
            onPressedRight != null ? () => onPressedRight(context) : null,
        colorButtonLeft: colorButtonLeft,
        textButtonLeft: textButtonLeft,
        colorBorderButtonLeft: colorBorderButtonLeft,
        onPressedLeft:
            onPressedLeft != null ? () => onPressedLeft(context) : null,
        textStyleLeft: textStyleLeft,
        contentWidget: contentWidget,
        allowDismiss: allowDismiss,
        barrierDismissible: barrierDismissible,
        iconSize: iconSize,
        padding: padding,
        imagePadding: imagePadding,
        titleStyle: titleStyle,
        showCloseIcon: showCloseIcon,
      );
    },
  );
}

Future showNotifyDialog({
  required BuildContext context,
  required String title,
  String? content,
  String? image,
  Color? colorButtonRight,
  String? textButtonRight,
  Color? colorBorderButtonRight,
  VoidCallback? onPressedRight,
  Color? colorButtonLeft,
  String? textButtonLeft,
  Color? colorBorderButtonLeft,
  VoidCallback? onPressedLeft,
  TextStyle? textStyleLeft,
  Widget? contentWidget,
  bool? allowDismiss,
  bool showCloseIcon = false,
  bool barrierDismissible = false,
  double? iconSize,
  EdgeInsets? padding,
  EdgeInsets? imagePadding,
  TextStyle? titleStyle,
}) {
  return showGeneralDialog(
    context: context,
    barrierDismissible: barrierDismissible,
    barrierLabel: '',
    pageBuilder: (context, _, __) {
      return BaseDialog(
        title: title,
        content: content,
        image: image,
        colorButtonRight: colorButtonRight,
        textButtonRight: textButtonRight,
        colorBorderButtonRight: colorBorderButtonRight,
        onPressedRight: onPressedRight,
        colorButtonLeft: colorButtonLeft,
        textButtonLeft: textButtonLeft,
        colorBorderButtonLeft: colorBorderButtonLeft,
        onPressedLeft: onPressedLeft,
        textStyleLeft: textStyleLeft,
        contentWidget: contentWidget,
        allowDismiss: allowDismiss,
        barrierDismissible: barrierDismissible,
        iconSize: iconSize,
        padding: padding,
        imagePadding: imagePadding,
        titleStyle: titleStyle,
        showCloseIcon: showCloseIcon,
      );
    },
  );
}

class BaseDialog extends StatelessWidget {
  const BaseDialog({
    required this.title,
    this.content,
    this.colorBackground,
    this.image,
    this.colorButtonRight,
    this.textButtonRight,
    this.colorBorderButtonRight,
    this.onPressedRight,
    this.colorButtonLeft,
    this.textButtonLeft,
    this.colorBorderButtonLeft,
    this.onPressedLeft,
    this.textStyleLeft,
    this.contentWidget,
    this.allowDismiss,
    this.barrierDismissible = false,
    this.iconSize,
    this.padding,
    this.imagePadding,
    this.titleStyle,
    this.showCloseIcon = false,
    this.onPressedIconClose,
    Key? key,
  }) : super(key: key);

  final TextStyle? titleStyle;

  final String title;

  final String? content;

  final String? image;

  final Color? colorButtonRight;

  final String? textButtonRight;

  final Color? colorBorderButtonRight;

  final VoidCallback? onPressedRight;

  final VoidCallback? onPressedIconClose;

  final Color? colorButtonLeft;

  final String? textButtonLeft;

  final Color? colorBorderButtonLeft;

  final VoidCallback? onPressedLeft;

  final TextStyle? textStyleLeft;

  final Widget? contentWidget;

  final bool? allowDismiss;

  final bool barrierDismissible;

  final double? iconSize;

  final EdgeInsets? padding;

  final EdgeInsets? imagePadding;

  final bool showCloseIcon;

  final Color? colorBackground;

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      margin: const EdgeInsets.symmetric(horizontal: 12),
      child: Material(
        color: colorBackground ?? themeData.bgPopup,
        borderRadius: BorderRadius.circular(12),
        child: WillPopScope(
          onWillPop: () => Future.value(allowDismiss ?? true),
          child: Stack(
            alignment: Alignment.topRight,
            children: [
              buildContentView(context),
              buildCloseIconWidget(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildContentView(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.fromLTRB(16, 42, 16, 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          /// Image view
          if (image != null)
            Padding(
              padding: imagePadding ?? const EdgeInsets.only(bottom: 35),
              child: SvgPicture.asset(
                image!,
                width: iconSize ?? 58,
                height: iconSize ?? 58,
                fit: BoxFit.fitWidth,
                package: VpUtilityAssets.package,
              ),
            ),

          /// title
          Text(
            title,
            textAlign: TextAlign.center,
            style:
                titleStyle ??
                vpTextStyle.headineBold6.copyColor(themeData.bgMain),
          ),

          const SizedBox(height: 8),

          if (content != null || contentWidget != null)
            content != null
                ? Text(
                  content!,
                  textAlign: TextAlign.center,
                  style: vpTextStyle.body14.copyColor(themeData.gray500),
                )
                : contentWidget!,

          const SizedBox(height: 16),

          /// button
          Row(
            children: [
              if (textButtonLeft != null)
                Expanded(
                  child: ButtonWidget(
                    textStyle:
                        textStyleLeft ??
                        vpTextStyle.body14.copyColor(themeData.gray700),
                    colorEnable: colorButtonLeft ?? Colors.transparent,
                    action: textButtonLeft,
                    onPressed: onPressedLeft,
                    colorBorder: colorBorderButtonLeft ?? themeData.borderPopUp,
                  ),
                ),
              Offstage(
                offstage: textButtonLeft == null || textButtonRight == null,
                child: const SizedBox(width: 8),
              ),
              if (textButtonRight != null)
                Expanded(
                  child: ButtonWidget(
                    colorEnable: colorButtonRight,
                    action: textButtonRight,
                    onPressed: onPressedRight,
                    colorBorder: colorBorderButtonRight,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildCloseIconWidget(BuildContext context) {
    if (!showCloseIcon) return const SizedBox(height: 0);

    return IconButton(
      padding: const EdgeInsets.all(12),
      onPressed:
          onPressedIconClose == null
              ? () => Navigator.pop(context)
              : () => onPressedIconClose?.call(),
      icon: Icon(Icons.clear, color: themeData.gray700),
    );
  }
}
