import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/widget/dialog/popup.dart';

class PopupDialog extends Popup {
  PopupDialog({
    super.key,
    required String title,
    double spacing = 10.0,
    required String content,
    super.iconPadding,
    super.buttonHeight,
    super.buttonSpacing,
    super.isHorizontalButton,
    super.background,
  }) : super(_Common(title: title, spacing: spacing, content: content));

  PopupDialog.builder(
    super.builder, {
    super.key,
    super.iconPadding,
    super.buttonHeight,
    super.buttonSpacing,
    super.isHorizontalButton,
    super.background,
  });

  @override
  Future show(BuildContext context) => showGeneralDialog(
    context: context,
    pageBuilder:
        (_, __, ___) => Dialog(
          insetPadding: const EdgeInsets.symmetric(horizontal: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          backgroundColor: themeData.bgPopup,
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                popupIcon(iconPadding),
                this,
                if (buttons != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: buttons,
                  ),
              ],
            ),
          ),
        ),
  );
}

class _Common extends StatelessWidget {
  final String title;
  final double spacing;
  final String content;

  const _Common({
    required this.title,
    required this.spacing,
    required this.content,
  });

  @override
  Widget build(BuildContext context) => Column(
    children: [
      Text(title, style: vpTextStyle.headineBold6),
      SizedBox(height: spacing),
      Text(content, style: vpTextStyle.subtitle14),
    ],
  );
}
