
import 'package:vp_utility/utils/input_field_state.dart';
import 'package:vp_utility/widget/input_filed/state/input_button_state.dart';
import 'package:vp_utility/widget/input_filed/state/input_suggest_state.dart';
import 'package:vp_utility/widget/input_filed/state/required_state.dart';

extension InputFieldStateExt on InputFieldState {
  InputButtonState? get inputButtonState =>
      this is InputButtonState ? this as InputButtonState : null;

  bool get isRequiredState => this is RequiredState;
}

extension InputButtonStateExt on InputButtonState {
  InputFieldState? get inputFieldState =>
      this is InputFieldState ? this as InputFieldState : null;
}

extension InputSuggestStateExt on InputSuggestState {
  InputFieldState? get inputFieldState =>
      this is InputFieldState ? this as InputFieldState : null;
}
