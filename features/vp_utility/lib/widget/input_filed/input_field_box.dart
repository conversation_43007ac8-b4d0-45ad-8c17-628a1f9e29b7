import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/utils/input_field_state.dart';
import 'package:vp_utility/widget/input_filed/ext/ext.dart';
import 'package:vp_utility/widget/input_filed/input_field_button.dart';
import 'package:vp_utility/widget/input_filed/input_field_clear.dart';
import 'package:vp_utility/widget/input_filed/utils/finder_keys.dart';

class InputFieldBox extends StatefulWidget {
  final InputFieldState state;
  final Color? errorTextColor;
  final Color? textColor;
  final Color? errorBackgroundColor;
  final Color? backgroundColor;
  final Color? errorBorderColor;
  final Color? borderColor;

  const InputFieldBox({
    super.key,
    required this.state,
    this.errorTextColor,
    this.textColor,
    this.errorBackgroundColor,
    this.backgroundColor,
    this.errorBorderColor,
    this.borderColor,
  });

  @override
  State<InputFieldBox> createState() => _InputFieldBoxState();
}

class _InputFieldBoxState extends State<InputFieldBox> {
  static const _unFocusOpacity = 0.16;

  bool get _hasFocus => widget.state.focusNode.hasFocus;

  Color get _textColor =>
      widget.state.isError
          ? widget.errorTextColor ?? themeData.black
          : themeData.black;

  Color get _backgroundColor =>
      widget.state.isError
          ? widget.errorBackgroundColor ?? themeData.red16
          : widget.backgroundColor ?? Colors.transparent;

  Color get _borderColor =>
      widget.state.isError
          ? widget.errorBorderColor ??
              themeData.red.withOpacity(_hasFocus ? 1.0 : _unFocusOpacity)
          : (widget.borderColor ?? themeData.borderBg).withOpacity(
            _hasFocus ? 1.0 : _unFocusOpacity,
          );

  Color get _subColor => _hasFocus ? themeData.black : themeData.black;

  @override
  Widget build(BuildContext context) => AnimatedBuilder(
    animation: widget.state.focusNode,
    builder:
        (_, __) => Stack(
          children: [
            AnimatedContainer(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: _backgroundColor,
                border: Border.all(width: 1, color: _borderColor),
              ),
              duration: DurationAnimation.ms100,
              curve: Curves.ease,
              child: Row(
                children: [
                  if (widget.state.inputButtonState != null)
                    InputFieldButton(
                      state: widget.state.inputButtonState!,
                      isEnd: false,
                      color: _subColor,
                    ),
                  if (widget.state.prefix(_subColor) != null)
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: widget.state.prefix(_subColor),
                    ),
                  Expanded(
                    child: TextField(
                      key: FinderKeys.inputFieldBox,
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        errorBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                        contentPadding: widget.state.contentPadding,
                        isDense: true,
                        hintText: widget.state.hintText,
                        hintStyle: vpTextStyle.subtitle14.copyColor(
                          themeData.gray500,
                        ),
                      ),
                      inputFormatters: widget.state.inputFormatters,
                      keyboardType: widget.state.keyboardType,
                      textAlign: widget.state.textAlign,
                      controller: widget.state.controller,
                      style: vpTextStyle.subtitle14.copyColor(_textColor),
                      focusNode: widget.state.focusNode,
                      maxLines: widget.state.maxLines,
                      autocorrect: false,
                      enableSuggestions: false,
                      enabled: widget.state.enabled,
                      scrollPadding: EdgeInsets.all(
                        widget.state.errorHeight + 8.0,
                      ),
                    ),
                  ),
                  InputFieldClear(state: widget.state),
                  if (widget.state.suffix(_subColor) != null)
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: widget.state.suffix(_subColor),
                    ),
                  if (widget.state.inputButtonState != null)
                    InputFieldButton(
                      state: widget.state.inputButtonState!,
                      isEnd: true,
                      color: _subColor,
                    ),
                ],
              ),
            ),
            if (!widget.state.enabled)
              Positioned.fill(
                child: AbsorbPointer(
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: themeData.highlightBg.withOpacity(0.7),
                    ),
                  ),
                ),
              ),
          ],
        ),
  );
}
