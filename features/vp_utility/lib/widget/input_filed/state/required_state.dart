import 'dart:async';

import 'package:vp_utility/utils/error_state.dart';
import 'package:vp_utility/utils/input_field_state.dart';

class RequiredState extends InputFieldState {
  final ErrorState requiredError;
  final StreamController<List> otherStream = StreamController<List>();
  List _other = [];
  StreamSubscription? _subscription;

  RequiredState(
      {required this.requiredError, super.controller, super.focusNode});

  addRequiredListener(Function(bool required) listener) =>
      _subscription = otherStream.stream.listen((_) => listener(isError));

  @override
  void fill(data) => super.fill(data ?? '');

  void other(List other) => otherStream.sink.add(_other = other);

  @override
  ErrorState? get emptyError =>
      _other.any((e) => e is InputFieldState ? e.isDone : e != null)
          ? requiredError
          : null;

  @override
  void removeListener() {
    _subscription?.cancel();
    otherStream.close();
    super.removeListener();
  }
}
