import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_utility/widget/input_filed/ext/ext.dart';
import 'package:vp_utility/widget/input_filed/state/input_button_state.dart';
import 'package:vp_utility/widget/input_filed/utils/finder_keys.dart';

class InputFieldButton extends StatelessWidget {
  final InputButtonState state;
  final bool isEnd;
  final Color color;

  const InputFieldButton({
    super.key,
    required this.state,
    required this.isEnd,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final asset = isEnd ? state.endAsset : state.startAsset;
    final icon = isEnd ? state.end(color) : state.start(color);
    return asset == null && icon == null
        ? const SizedBox.shrink()
        : GestureDetector(
            onTap: () => state.inputFieldState?.fill(state.tapButton(isEnd)),
            behavior: HitTestBehavior.translucent,
            key:
                isEnd ? FinderKeys.inputEndButton : FinderKeys.inputStartButton,
            child: CircleAvatar(
              radius: state.buttonSize / 2,
              backgroundColor: Colors.transparent,
              child: icon ??
                  SvgPicture.asset(
                    asset!,
                    colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
                  ),
            ),
          );
  }
}
