import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_utility/generated/assets.gen.dart';
import 'package:vp_utility/utils/input_field_state.dart';

class InputField<PERSON>lear extends StatelessWidget {
  final InputFieldState state;

  const InputFieldClear({super.key, required this.state});

  @override
  Widget build(BuildContext context) => AnimatedSwitcher(
    duration: const Duration(milliseconds: 200),
    child:
        state.clear && !state.isEmpty
            ? Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: GestureDetector(
                onTap: () => state.fill(''),
                child: SvgPicture.asset(
                  VpUtilityAssets.icons.icClearText.path,
                  package: VpUtilityAssets.package,
                ),
              ),
            )
            : const SizedBox.shrink(),
  );
}
