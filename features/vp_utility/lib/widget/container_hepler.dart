import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class ContainerHelper {
  static decorationBottom() {
    return BoxDecoration(
        color: themeData.bgPopup,
        borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12)));
  }

  static decorationBottomAll() {
    return BoxDecoration(
        color: themeData.bgPopup,
        borderRadius: BorderRadius.circular(12));
  }
}
