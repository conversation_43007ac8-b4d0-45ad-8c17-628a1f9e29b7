import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/assets.gen.dart';

class AppRangeTimeView extends StatelessWidget {
  const AppRangeTimeView({super.key, required this.value});

  final String value;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(width: 1, color: themeData.gray500),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            VpUtilityAssets.icons.icDatePicker.path,
            package: VpUtilityAssets.package,
            colorFilter: ColorFilter.mode(themeData.colorIcon, BlendMode.srcIn),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: vpTextStyle.body14.copyColor(themeData.text),
            ),
          ),
        ],
      ),
    );
  }
}
