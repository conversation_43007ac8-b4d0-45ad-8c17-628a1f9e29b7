import 'package:flutter/material.dart';

class SlideSwitcher extends StatelessWidget {
  final Widget child;
  final Offset begin;
  final bool show;
  final Duration duration;

  const SlideSwitcher({
    super.key,
    required this.child,
    this.begin = const Offset(0, -1),
    required this.show,
    this.duration = const Duration(milliseconds: 100),
  });

  @override
  Widget build(BuildContext context) => ClipRect(
        child: AnimatedSwitcher(
          switchInCurve: Curves.ease,
          switchOutCurve: Curves.ease,
          duration: duration,
          child: show ? child : const SizedBox.shrink(),
          transitionBuilder: (child, animation) => SlideTransition(
            position: Tween<Offset>(begin: begin, end: Offset.zero)
                .animate(animation),
            child: child,
          ),
        ),
      );
}
