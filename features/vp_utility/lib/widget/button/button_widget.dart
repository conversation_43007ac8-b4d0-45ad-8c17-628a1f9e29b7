import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';

class ButtonWidget extends StatelessWidget {
  const ButtonWidget({
    super.key,
    this.onPressed,
    this.enable = true,
    this.colorEnable,
    this.action,
    this.colorBorder,
    this.colorDisable,
    this.borderRadius = 12,
    this.actionWidget,
    this.textStyle,
    this.padding,
  });

  final VoidCallback? onPressed;
  final String? action;
  final Widget? actionWidget;
  final bool enable;
  final Color? colorEnable;
  final Color? colorBorder;
  final Color? colorDisable;
  final double borderRadius;
  final TextStyle? textStyle;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed:
          enable && onPressed != null
              ? () => NoDuplicate(() => onPressed!.call())
              : null,
      style: ButtonStyle(
        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            side: BorderSide(
              width: 1,
              color: colorBorder ?? Colors.transparent,
            ),
          ),
        ),
        padding: WidgetStateProperty.all<EdgeInsets>(
          padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        backgroundColor: WidgetStateProperty.all<Color>(
          enable
              ? (colorEnable ?? themeData.primary)
              : (colorDisable ?? themeData.buttonDisableBg),
        ),
      ),
      child: Container(
        alignment: Alignment.center,
        child:
            actionWidget ??
            Text(
              action ?? '',
              style:
                  textStyle ??
                  (enable
                      ? vpTextStyle.subtitle14.copyColor(themeData.textEnable)
                      : vpTextStyle.subtitle14.copyColor(themeData.gray500)),
              textAlign: TextAlign.center,
            ),
      ),
    );
  }
}

class NoDuplicate {
  static int previousTime = 0;

  NoDuplicate(Function function, {int inMilliseconds = 500}) {
    final now = DateTime.now().millisecondsSinceEpoch;
    if ((now - previousTime).abs() >= inMilliseconds) {
      function();
    }
    previousTime = now;
  }
}