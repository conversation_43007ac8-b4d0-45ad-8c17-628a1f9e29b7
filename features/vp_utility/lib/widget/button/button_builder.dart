import 'package:flutter/material.dart';
import 'package:vp_utility/widget/button/button_type.dart';

class Build extends StatelessWidget with ButtonType {
  late final Params params;
  late final Widget Function(Params) _builder;

  Build.outline({
    super.key,
    required String label,
    required VoidCallback onPressed,
  }) {
    params = Params(label: label, onPressed: onPressed);
    _builder = (builder) => outline(builder);
  }

  Build.normal({
    super.key,
    required String label,
    required VoidCallback onPressed,
    bool enable = true,
    Color? colorEnable,
  }) {
    params = Params(
      label: label,
      onPressed: onPressed,
      enable: enable,
      colorEnable: colorEnable,
    );
    _builder = (builder) => normal(builder);
  }

  @override
  Widget build(BuildContext context) => _builder(params);
}
