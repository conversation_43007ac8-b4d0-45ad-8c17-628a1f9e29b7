import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/widget/button/button_widget.dart';

class Params {
  final String label;
  final VoidCallback? onPressed;
  final bool enable;
  final Color? colorEnable;

  Params({
    required this.label,
    this.onPressed,
    this.enable = true,
    this.colorEnable,
  });
}

mixin ButtonType {
  Widget outline(Params params) => ButtonWidget(
    action: params.label,
    colorEnable: themeData.bgPopup,
    colorBorder: themeData.borderPopUp,
    textStyle: vpTextStyle.subtitle14.copyColor(themeData.black),
    onPressed: params.onPressed,
  );

  Widget normal(Params params) => ButtonWidget(
    action: params.label,
    onPressed: params.onPressed,
    colorEnable: params.colorEnable,
    enable: params.enable,
  );
}
