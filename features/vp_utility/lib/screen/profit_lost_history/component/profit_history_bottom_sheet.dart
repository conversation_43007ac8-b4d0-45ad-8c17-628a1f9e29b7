import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/screen/profit_lost_history/model/profit_lost_history_model.dart';

class ProfitHistoryBottomSheet extends StatefulWidget {
  const ProfitHistoryBottomSheet({super.key, required this.item});

  final ProfitLostHistoryModel item;

  @override
  State<ProfitHistoryBottomSheet> createState() =>
      _ProfitHistoryBottomSheetState();
}

class _ProfitHistoryBottomSheetState extends State<ProfitHistoryBottomSheet> {
  num get profit => item.pnl ?? 0;

  ProfitLostHistoryModel get item => widget.item;

  @override
  Widget build(BuildContext context) {
    final buyPrice = item.costpriceI?.toDouble().getPriceFormatted(
      convertToThousand: true,
    );

    final sellPrice = item.costpriceO?.toDouble().getPriceFormatted(
      convertToThousand: true,
    );

    final profitFormat = profit.toMoney(addCharacter: true);

    return BaseBottomSheet(
      children: [
        /// Mã
        ...buildItem(
          title: trans(MoneyKeyLang.profitHistorySymbol),
          content: item.symbol,
          contentStyle: TextStyleUtils.text14Weight600.copyWith(
            color: ColorUtils.black,
          ),
        ),

        /// Ngày bán
        ...buildItem(
          title: trans(MoneyKeyLang.profitHistorySellDate),
          content: item.txdate?.toDate(),
        ),

        /// Khối lượng
        ...buildItem(
          title: trans(MoneyKeyLang.profitHistoryWeight),
          content: NumberUtils.format(item.netqtty),
        ),

        /// Giá mua
        ...buildDivider(),
        ...buildItem(
          title: trans(MoneyKeyLang.profitHistoryBuyPrice),
          content: buyPrice,
        ),

        /// Giá trị mua
        ...buildItem(
          title: trans(MoneyKeyLang.profitHistoryBuyValue),
          content: item.netvalue?.toDouble().toMoney(),
        ),

        /// giá bán
        ...buildDivider(),
        ...buildItem(
          title: trans(MoneyKeyLang.profitHistorySellPrice),
          content: sellPrice,
        ),

        /// Giá trị bán
        ...buildItem(
          title: trans(MoneyKeyLang.profitHistorySellValue),
          content: item.sellvalue?.toDouble().toMoney(),
        ),

        /// lãi/lỗ
        ...buildDivider(),
        ...buildItem(
          title: trans(MoneyKeyLang.profitHistoryProfit),
          content: '$profitFormat (${item.profitPercent})',
          contentStyle: TextStyleUtils.text14Weight600.copyWith(
            color: CommonColorUtils.colorValue(profit),
          ),
        ),

        /// Button Đóng
        const SizedBox(height: 25),

        ButtonWidget(
          onPressed: () => Navigator.pop(context),
          colorEnable: ColorUtils.transparent,
          colorBorder: ColorUtils.borderPopUp,
          action: trans(MoneyKeyLang.profitHistoryButtonClose),
          textStyle: TextStyleUtils.text14Weight600.copyWith(
            color: ColorUtils.gray700,
          ),
        ),
      ],
    );
  }

  List<Widget> buildDivider() {
    return [const SizedBox(height: 10), Divider(color: ColorUtils.divider)];
  }

  List<Widget> buildItem({
    required String title,
    required String? content,
    TextStyle? contentStyle,
  }) {
    return [
      const SizedBox(height: 10),
      Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: vpTextStyle.body14.copyColor(vpColor.gray700),
            ),
          ),
          Text(
            content ?? '',
            style: contentStyle ?? vpTextStyle.body14.copyColor(vpColor.black),
          ),
        ],
      ),
    ];
  }

  Color get color {
    if (profit < 0) return ColorUtils.decreaseColor;

    if (profit > 0) return ColorUtils.increaseColor;

    return ColorUtils.referenceColor;
  }
}
