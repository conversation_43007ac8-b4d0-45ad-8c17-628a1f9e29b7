import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/l10n.dart';

enum TypeRcEnum { buy, sell, hold, unknown }

extension TypeRcEnumExtension on TypeRcEnum {
  String get localizedText {
    switch (this) {
      case TypeRcEnum.buy:
        return VpUtilityLocalize.current.rc_buy;
      case TypeRcEnum.sell:
        return VpUtilityLocalize.current.rc_sell;
      case TypeRcEnum.hold:
        return VpUtilityLocalize.current.rc_holding;
      default:
        return '';
    }
  }

  Color get color {
    switch (this) {
      case TypeRcEnum.buy:
        return Theme.of(getContext).primary;
      case TypeRcEnum.sell:
        return Theme.of(getContext).red;
      case TypeRcEnum.hold:
        return Theme.of(getContext).yellow;
      default:
        return Colors.transparent;
    }
  }

  // màn danh sách broker
  String get localizedTextBroker {
    switch (this) {
      case TypeRcEnum.buy:
        return "Lệnh mua";
      case TypeRcEnum.sell:
        return "Lệnh bán";
      case TypeRcEnum.hold:
        return VpUtilityLocalize.current.rc_holding;
      default:
        return '';
    }
  }

  static TypeRcEnum parseTypeRcEnum(String? value) {
    switch ((value ?? '').toUpperCase()) {
      case 'BUY':
        return TypeRcEnum.buy;
      case 'SELL':
        return TypeRcEnum.sell;
      case 'HOLD':
        return TypeRcEnum.hold;
      default:
        return TypeRcEnum.unknown;
    }
  }
}
