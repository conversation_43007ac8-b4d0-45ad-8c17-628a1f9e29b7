import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/color_exts.dart';
import 'package:vp_stock_common/model/enum/order_action.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_utility/cubit/recommendation_home/rc_info_bts/rc_info_bts_bloc.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/response/recommendation/recommendation_info_model.dart';
import 'package:vp_utility/screen/recommendation_home/enum/type_rc_enum.dart';
import 'package:vp_utility/screen/recommendation_home/widget/content_expansion_widget.dart';
import 'package:vp_utility/screen/recommendation_home/widget/investment_thesis_bottom_sheet.dart';

void showRcInfoBottomSheet(
  BuildContext context,
  RecommendationInfoModel model, {
  bool isHome = false,
  required bool isNCP,
}) async {
  final colorUtils = Theme.of(context);
  return await showModalBottomSheet(
    barrierColor: colorUtils.overlayBottomSheet,
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    useSafeArea: true,
    builder: (BuildContext context) {
      return BaseBottomSheet(
        children: [
          RecmdtInfoWidget(model: model, isHome: isHome, isNCP: isNCP),
        ],
      );
    },
  );
}

class RecmdtInfoWidget extends StatefulWidget {
  const RecmdtInfoWidget({
    super.key,
    required this.model,
    required this.isHome,
    required this.isNCP,
  });

  final RecommendationInfoModel model;
  final bool isHome;
  final bool isNCP;

  @override
  State<RecmdtInfoWidget> createState() => _RecmdtInfoWidgetState();
}

class _RecmdtInfoWidgetState extends State<RecmdtInfoWidget> {
  bool sell = false;
  bool isNCP = true;
  bool hasLink = false;

  @override
  void initState() {
    super.initState();
    sell = widget.model.typeRc == 'SELL';
    isNCP = widget.isNCP;
    hasLink = widget.model.reasonLink != null;
  }

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return BlocProvider(
      create: (context) => RcInfoBtsBloc()..init(widget.model),
      child: SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 4 / 5,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(height: 24),
                      RcInfoHeader(model: widget.model),
                      SizedBox(height: 16),
                      RcInfoBasicInfoSection(model: widget.model, isNCP: isNCP),
                      SizedBox(height: 24),
                      DividerWidget(),
                      SizedBox(height: 16),
                      RcInfoDateSection(model: widget.model),
                      SizedBox(height: 24),
                      RcInfoInvestmentThesisSection(
                        model: widget.model,
                        hasLink: hasLink,
                        isNCP: isNCP,
                      ),
                      SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
              RcInfoActionButton(
                model: widget.model,
                isHome: widget.isHome,
                isNCP: isNCP,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool> openBrowser(String url, {LaunchMode? mode}) async {
    try {
      Uri uri = Uri.parse(url);
      return await launchUrl(
        uri,
        mode:
            mode ??
            (Platform.isAndroid
                ? LaunchMode.externalApplication
                : LaunchMode.platformDefault),
      );
    } catch (error) {
      dlog(error);
      return false;
    }
  }
}

class RcInfoHeader extends StatelessWidget {
  final RecommendationInfoModel model;
  const RcInfoHeader({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return Row(
      children: [
        Expanded(
          child: Text(
            model.symbol ?? '',
            style: context.textStyle.subtitle14?.copyWith(
              color: vpColor.textPrimary,
            ),
          ),
        ),
        SizedBox(width: 16),
        Container(
          padding: const EdgeInsets.all(4).copyWith(bottom: 6),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: model.active ? colorUtils.primary16 : colorUtils.red16,
          ),
          child: Text(
            model.active ? VpUtilityLocalize.current.rc_active : VpUtilityLocalize.current.rc_status_expired,
            style: context.textStyle.captionRegular?.copyWith(
              color: vpColor.textWhite,
            ),
          ),
        ),
      ],
    );
  }
}

class RcInfoBasicInfoSection extends StatelessWidget {
  final RecommendationInfoModel model;
  final bool isNCP;
  const RcInfoBasicInfoSection({
    super.key,
    required this.model,
    required this.isNCP,
  });

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        ContentExpansionWidget(
          textStyle: context.textStyle.body14?.copyWith(
            color: model.typeRcEnum.color,
            fontWeight: FontWeight.w500,
          ),
          title: VpUtilityLocalize.current.rc_typeRecommendation,
          value: model.typeRcEnum.localizedText.toUpperCase(),
        ),
        isNCP
            ? ContentExpansionWidget(
              title: VpUtilityLocalize.current.rc_targetPriceVsCurrent,
              value: '--',
            )
            : BlocBuilder<RcInfoBtsBloc, RcInfoBtsState>(
              builder:
                  (context, state) => ContentExpansionWidget(
                    textStyle: context.textStyle.body14?.copyWith(
                      color: CommonColorUtils.colorValue(state.percent),
                    ),
                    title: VpUtilityLocalize.current.rc_targetPriceVsCurrent,
                    value:
                        state.percent != null
                            ? state.percent!.toDouble().getChangePercentDisplay(
                              addCharacter: true,
                            )
                            : '--',
                  ),
            ),
        ContentExpansionWidget(
          textStyle: context.textStyle.body14?.copyWith(
            color:
                model.oe != null
                    ? CommonColorUtils.colorValue(
                      double.tryParse(model.oe ?? "0"),
                    )
                    : colorUtils.black,
            fontWeight: FontWeight.w500,
          ),
          title: VpUtilityLocalize.current.rc_operatingEfficiency,
          value:
              model.oe != null
                  ? double.tryParse(
                    model.oe ?? "0",
                  )!.getChangePercentDisplay(addCharacter: true)
                  : '--',
        ),
        ContentExpansionWidget(
          title: VpUtilityLocalize.current.rc_method,
          value: model.getMethod,
        ),
        ContentExpansionWidget(
          title: VpUtilityLocalize.current.rc_targetPrice,
          value: model.getValuePrice(model.targetPrice, empty: '--'),
        ),
        ContentExpansionWidget(
          title: VpUtilityLocalize.current.rc_stopLossPrice,
          value: model.getValuePrice(model.stopLossPrice, empty: '--'),
        ),
      ],
    );
  }
}

class RcInfoDateSection extends StatelessWidget {
  final RecommendationInfoModel model;
  const RcInfoDateSection({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        ContentExpansionWidget(
          title: VpUtilityLocalize.current.rc_recommendedDate,
          value: AppTimeUtils.getDateTimeString(dateTime: model.from),
        ),
        ContentExpansionWidget(
          title: VpUtilityLocalize.current.rc_expiryDate,
          value: AppTimeUtils.parseStringToString(
            model.toDate,
            AppTimeUtilsFormat.dateYMD,
            AppTimeUtilsFormat.dateNormal,
          ),
        ),
        ContentExpansionWidget(
          title: VpUtilityLocalize.current.rc_investmentTime,
          value: model.invTime,
        ),
      ],
    );
  }
}

class RcInfoInvestmentThesisSection extends StatelessWidget {
  final RecommendationInfoModel model;
  final bool hasLink;
  final bool isNCP;
  const RcInfoInvestmentThesisSection({
    super.key,
    required this.model,
    required this.hasLink,
    required this.isNCP,
  });

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    if (model.shortDescription == null || model.shortDescription == '') {
      return const SizedBox();
    }
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: colorUtils.highlightBg,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            VpUtilityLocalize.current.rc_investmentThesis,
            style: context.textStyle.captionRegular?.copyWith(
              color: colorUtils.gray700,
            ),
          ),
          SizedBox(height: 8),
          BlocBuilder<RcInfoBtsBloc, RcInfoBtsState>(
            buildWhen: (p, c) => p.seeMore != c.seeMore,
            builder:
                (context, state) => RichText(
                  text: TextSpan(
                    text:
                        (!state.seeMore)
                            ? state.investmentThesis
                            : '${state.investmentThesis.substring(0, 140)}...',
                    style:
                        hasLink
                            ? context.textStyle.captionRegular?.copyWith(
                              color: colorUtils.blue,
                              decoration: TextDecoration.underline,
                            )
                            : context.textStyle.captionRegular?.copyWith(
                              color: colorUtils.gray700,
                            ),
                    recognizer:
                        model.reasonLink != null
                            ? (TapGestureRecognizer()
                              ..onTap =
                                  () => _RecmdtInfoWidgetState().openBrowser(
                                    model.reasonLink ?? '',
                                  ))
                            : null,
                  ),
                ),
          ),
          BlocBuilder<RcInfoBtsBloc, RcInfoBtsState>(
            buildWhen: (p, c) => p.seeMore != c.seeMore,
            builder:
                (context, state) => Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Visibility(
                    visible: state.viewSeeMore,
                    child: Center(
                      child: InkWell(
                        onTap:
                            () => context.read<RcInfoBtsBloc>().updateSeeMore(),
                        child: Text(
                          !state.seeMore
                              ? VpUtilityLocalize.current.rc_collapse
                              : VpUtilityLocalize.current.rc_seeMore,
                          style: context.textStyle.captionMedium?.copyWith(
                            color: colorUtils.primary,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
          ),
          BlocBuilder<RcInfoBtsBloc, RcInfoBtsState>(
            builder: (context, state) {
              return GestureDetector(
                onTap: () {
                  showInvestmentThesisBottomSheet(
                    context,
                    state.descriptionHtml ?? "",
                  );
                },
                child: Center(
                  child: Text(
                    VpUtilityLocalize.current.rc_seeMore,
                    style: context.textStyle.captionMedium?.copyWith(
                      color: colorUtils.primary,
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class RcInfoActionButton extends StatelessWidget {
  final RecommendationInfoModel model;
  final bool isHome;
  final bool isNCP;
  const RcInfoActionButton({
    super.key,
    required this.model,
    required this.isHome,
    required this.isNCP,
  });

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return VPDynamicButton.primarySmall(
      text:
          isHome
              ? (model.typeRcEnum == TypeRcEnum.sell
                  ? VpUtilityLocalize.current.rc_sell
                  : VpUtilityLocalize.current.rc_buy)
              : (model.typeRcEnum == TypeRcEnum.sell
                  ? VpUtilityLocalize.current.rc_placeSellOrder
                  : VpUtilityLocalize.current.rc_placeBuyOrder),
      onTap: () {
        context.pop();
        context.pushNamed(
          '/placeOrder',
          queryParameters: PlaceOrderArgs(
            symbol: model.symbol ?? "VPB",
            action:
                model.typeRcEnum == TypeRcEnum.sell
                    ? OrderAction.sell
                    : OrderAction.buy,
          ).toQueryParams(),
        );
      },
      backgroundColor:
          model.typeRcEnum == TypeRcEnum.sell
              ? colorUtils.red
              : colorUtils.primary,
    );
  }
}
