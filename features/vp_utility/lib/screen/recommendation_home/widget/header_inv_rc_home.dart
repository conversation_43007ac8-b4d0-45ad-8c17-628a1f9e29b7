import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/cubit/recommendation_home/inv_rc/inv_rc_cubit.dart';
import 'package:vp_utility/router/vp_utility_router.dart';
import 'package:vp_utility/screen/recommendation_home/enum/recommendation_type.dart';
import 'package:vp_utility/screen/recommendation_home/widget/rc_one_select_item.dart';

class HeaderInvRecommendationHome extends StatefulWidget {
  const HeaderInvRecommendationHome({super.key, required this.hasBroker});

  final bool hasBroker;

  @override
  State<HeaderInvRecommendationHome> createState() =>
      _HeaderInvRecommendationHomeState();
}

class _HeaderInvRecommendationHomeState
    extends State<HeaderInvRecommendationHome> {
  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    var recommendationType =
        context.watch<InvRecommendationCubit>().state.recommendationType;

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              "Khuyến nghị",
              style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
            ),
            Spacer(),
            GestureDetector(
              onTap: () {
                context.push(
                  VpUtilityRouter.recommendationList.routeName,
                  extra: recommendationType,
                );
              },
              child: Icon(
                Icons.arrow_forward_ios_sharp,
                color: colorUtils.textEnable,
                size: 16,
              ),
            ),
          ],
        ),
        SizedBox(height: 16),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              ...RecommendationTypeExtention.valuesByHasBroker(
                widget.hasBroker,
              ).map((type) {
                return RCOneSelectItem<RecommendationType>(
                  value: type,
                  selected: recommendationType == type,
                  onSelected: (e) {
                    //  var customerInfo = GetIt.instance<AuthCubit>().getCustomerInfoIam;
                    context
                        .read<InvRecommendationCubit>()
                        .updateHeaderInvRecommendation(e);
                  },
                  displayText: type.title,
                );
              }),
            ],
          ),
        ),
      ],
    );
  }
}
