import 'package:flutter/material.dart';
import 'package:vp_common/utils/navigation_service.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/response/recommendation/broker_recommendation_model.dart';
import 'package:vp_utility/screen/recommendation_home/enum/type_rc_enum.dart';
import 'package:vp_utility/screen/recommendation_home/widget/rc_broker_detail_bottom_sheet.dart';

class BrokerWidget extends StatefulWidget {
  final BrokerRecommendationModel data;

  const BrokerWidget({super.key, required this.data});

  @override
  State<BrokerWidget> createState() => _BrokerWidgetState();
}

class _BrokerWidgetState extends State<BrokerWidget> {
  ThemeData get colorUtils => Theme.of(getContext);

  @override
  Widget build(BuildContext context) {
    String minMaxRecommendedPrice({required String min, required String max}) {
      if (min.isEmpty) {
        return '${VpUtilityLocalize.current.rc_less} $max';
      } else if (max.isEmpty) {
        return '${VpUtilityLocalize.current.rc_bigger} $min';
      }
      return '$min - $max';
    }

    return InkWell(
      onTap:
          () => showModalBottomSheet(
            barrierColor: colorUtils.overlayBottomSheet,
            context: context,
            isDismissible: true,
            useSafeArea: true,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            builder: (context) {
              return BaseBottomSheet(
                children: [
                  RecommendationDetailBottomSheet(
                    stockOrderId: int.parse(widget.data.stockOrderId ?? '0'),
                  ),
                ],
              );
            },
          ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Row(
          children: [
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.data.symbol ?? '-',
                    style: context.textStyle.subtitle14?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                  Text(
                    minMaxRecommendedPrice(
                      min: (widget.data.minRecommendedPrice ?? '').toString(),
                      max: (widget.data.maxRecommendedPrice ?? '').toString(),
                    ),
                    style: context.textStyle.body14?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 16),

            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: const EdgeInsets.only(
                      left: 4,
                      right: 4,
                      top: 2,
                      bottom: 4,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: widget.data.typeRcEnum.color,
                    ),
                    child: Text(
                      widget.data.typeRcEnum.localizedText.toUpperCase(),

                      textAlign: TextAlign.center,
                      style: context.textStyle.captionRegular?.copyWith(
                        color: vpColor.textWhite,
                      ),
                    ),
                  ),
                  Text(
                    "${VpUtilityLocalize.current.rc_expire}: ${widget.data.expiredDate}",
                    style: context.textStyle.captionRegular?.copyWith(
                      color: vpColor.textSecondary,
                    ),
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.end,
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
