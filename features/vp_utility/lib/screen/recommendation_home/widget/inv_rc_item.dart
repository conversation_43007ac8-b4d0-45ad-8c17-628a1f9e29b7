import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_utility/cubit/recommendation_home/inv_rc/inv_rc_cubit.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/response/recommendation/recommendation_info_model.dart';
import 'package:vp_utility/screen/recommendation_home/enum/recommendation_type.dart';
import 'package:vp_utility/screen/recommendation_home/enum/type_rc_enum.dart';
import 'package:vp_utility/screen/recommendation_home/widget/rc_info_bottom_sheet.dart';

class InvRecommendationItem extends StatelessWidget {
  final RecommendationInfoModel model;

  const InvRecommendationItem({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    var isNCP =
        context.watch<InvRecommendationCubit>().state.recommendationType ==
        RecommendationType.vpbanks;
    return InkWell(
      onTap:
          () =>
              showRcInfoBottomSheet(context, model, isHome: true, isNCP: isNCP),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    text: model.symbol ?? '',
                    style: context.textStyle.subtitle14?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                    children: [
                      TextSpan(
                        text:
                            '   ${model.getValuePrice(model.expectPriceBmin)} - ${model.getValuePrice(model.expectPriceBmax)}',
                        style: context.textStyle.body14?.copyWith(
                          color: vpColor.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  "Giá mục tiêu: ${model.getValuePrice(model.targetPrice)}",
                  style: context.textStyle.captionSemiBold?.copyWith(
                    color: vpColor.textSecondary,
                  ),
                ),
              ],
            ),

            SizedBox(width: 16),

            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: const EdgeInsets.only(
                      left: 4,
                      right: 4,
                      top: 2,
                      bottom: 4,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: model.typeRcEnum.color,
                    ),
                    child: Text(
                      model.typeRcEnum.localizedText.toUpperCase(),
                      textAlign: TextAlign.center,
                      style: context.textStyle.captionRegular?.copyWith(
                        color: vpColor.textWhite,
                      ),
                    ),
                  ),
                  Text(
                    "${VpUtilityLocalize.current.rc_expire}: ${AppTimeUtils.parseStringToString(model.toDate, AppTimeUtilsFormat.dateYMD, AppTimeUtilsFormat.dateNormal)}",
                    style: context.textStyle.captionRegular?.copyWith(
                      color: vpColor.textSecondary,
                    ),
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.end,
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
