import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/divider_widget.dart';
import 'package:vp_design_system/custom_widget/no_data_view.dart';
import 'package:vp_utility/cubit/recommendation_home/inv_rc/inv_rc_cubit.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/response/recommendation/broker_recommendation_model.dart';
import 'package:vp_utility/model/response/recommendation/recommendation_info_model.dart';
import 'package:vp_utility/screen/recommendation_home/enum/recommendation_type.dart';
import 'package:vp_utility/screen/recommendation_home/widget/broker_widget.dart';
import 'package:vp_utility/screen/recommendation_home/widget/header_inv_rc_home.dart';
import 'package:vp_utility/screen/recommendation_home/widget/inv_rc_item.dart';
import 'package:vp_utility/screen/recommendation_home/widget/inv_rc_loading.dart';

class InvRecommendationView extends StatefulWidget {
  const InvRecommendationView({super.key});

  @override
  State<InvRecommendationView> createState() => _InvRecommendationViewState();
}

class _InvRecommendationViewState extends State<InvRecommendationView> {
  bool hasBroker = false;
  @override
  void initState() {
    super.initState();
    _checkBroker();
  }

  Future<void> _checkBroker() async {
    var customerInfo = await GetIt.instance<AuthCubit>().getCustomerInfoIam();
    var hasBrokerData = customerInfo?.brokerNo != null;
    setState(() {
      hasBroker = hasBrokerData;
    });
  }

  //  hasBroker = Session().iamUserInfo?.brokerNo != null;
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => InvRecommendationCubit()..getData(),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: vpColor.backgroundElevation0,
          boxShadow: const [
            BoxShadow(
              color: Color(0x1E2A3346),
              blurRadius: 32,
              offset: Offset(0, 16),
              spreadRadius: 0,
            ),
            BoxShadow(
              color: Color(0x0A2A3346),
              blurRadius: 32,
              offset: Offset(0, 4),
              spreadRadius: 0,
            ),
          ],
        ),
        child: BlocConsumer<InvRecommendationCubit, InvRecommendationState>(
          listener: (context, state) {},
          builder: (context, state) {
            return Column(
              children: [
                HeaderInvRecommendationHome(hasBroker: hasBroker),

                if (state.isLoading)
                  InvRcLoading(itemCount: 4, showPadding: false),

                if (!state.isLoading)
                  state.recommendationType == RecommendationType.broker
                      ? _buildListBroker(state.dataBroker)
                      : _buildListInvRecommendation(state.data),
              ],
            );
          },
        ),
      ),
    );
  }

  _buildListInvRecommendation(List<RecommendationInfoModel> data) {
    if (data.isEmpty) {
      return NoDataView(content: VpUtilityLocalize.current.rc_no_data);
    }
    return ListView.separated(
      shrinkWrap: true,
      itemBuilder: (_, index) {
        final item = data[index];
        return InvRecommendationItem(model: item);
      },
      separatorBuilder: (_, index) => DividerWidget(),
      physics: const NeverScrollableScrollPhysics(),
      itemCount: data.length > 4 ? 4 : data.length,
      padding: EdgeInsets.zero,
    );
  }

  _buildListBroker(List<BrokerRecommendationModel> dataBroker) {
    if (dataBroker.isEmpty) {
      return NoDataView(content: VpUtilityLocalize.current.rc_no_data);
    }
    return ListView.separated(
      shrinkWrap: true,
      itemBuilder: (_, index) {
        return BrokerWidget(data: dataBroker[index]);
      },
      separatorBuilder: (_, index) => DividerWidget(),
      physics: const NeverScrollableScrollPhysics(),
      itemCount: dataBroker.length > 4 ? 4 : dataBroker.length,
      padding: EdgeInsets.zero,
    );
  }
}
