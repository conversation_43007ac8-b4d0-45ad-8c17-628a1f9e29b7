import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/screen/derivative_statement/bloc/derivative_statement_bloc.dart';

import 'data/constants/transaction_type_value_constant.dart';

class DerivativeStatementPage extends StatefulWidget {
  const DerivativeStatementPage({Key? key, this.derivativeAccount = false})
    : super(key: key);

  final bool derivativeAccount;

  @override
  State<DerivativeStatementPage> createState() =>
      _DerivativeStatementPageState();
}

class _DerivativeStatementPageState extends State<DerivativeStatementPage> {
  late DerivativeStatementBloc _bloc;

  @override
  void initState() {
    _bloc = DerivativeStatementBloc();
    // _bloc.initFilterDefault();
    _bloc.onLoadData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<DerivativeStatementBloc>(
      create: (cxt) => _bloc,
      child: Scaffold(
        body: SafeArea(
          child: Column(
            children: [
              BlocBuilder<DerivativeStatementBloc, DerivativeStatementState>(
                builder:
                    (context, state) => HeaderWidget(
                      subTitle: getDerivativesLang(DerivativesKeyLang.service),
                      title: getDerivativesLang(
                        DerivativesKeyLang.marginStatement,
                      ),
                      icon:
                          _bloc.haveFilter
                              ? Assets.icons.icHaveFilter.svg()
                              : Assets.icons.icFilter.svg(),
                      actionRight:
                          () => pickDate(
                            transactionType: state.selectedTransactionType,
                          ),
                    ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 12, 16),
                      child: Row(
                        children: [
                          Text(
                            getDerivativesLang(DerivativesKeyLang.subAccount),
                            style: TextStyleUtils.text14Weight500,
                          ),
                          const Spacer(),
                          Text(
                            getDerivativesLang(DerivativesKeyLang.derivative),
                            style: TextStyleUtils.text14Weight500.copyWith(
                              color: ColorUtils.gray700,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(child: buildContentView()),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future pickDate({TransactionType? transactionType}) async {
    final value = await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder:
          (_) => ProfitHistoryFilterView(
            maxDate: _bloc.maxDate,
            startDate: _bloc.startDate,
            endDate: _bloc.endDate,
            resetEndDate: _bloc.resetEndDate,
            resetStartDate: _bloc.resetStartDate,
            initialTransactionType: transactionType ?? TransactionType.all,
          ),
    );
    if (value is Tuple3) {
      _bloc.onDatePicked([
        value.item1,
        value.item2,
      ], value.item3 ?? TransactionType.all);
    }
  }

  Widget buildContentView() {
    return BlocBuilder<DerivativeStatementBloc, DerivativeStatementState>(
      builder: (context, state) {
        if (state.isLoading) {
          return HomeStockLoading(count: 10);
        }
        if (state is MoneyStatementErrorState) {
          return ErrorView(
            showErrorImage: true,
            responseError: state.error,
            onTryAgain: () {
              _bloc.onRefresh();
            },
            margin: const EdgeInsets.symmetric(horizontal: 16),
          );
        }
        return PullToRefreshView(
          onRefresh: () async {
            _bloc.onRefresh();
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ItemInfoWidget(
                title: getDerivativesLang(DerivativesKeyLang.openingBalance),
                value: _bloc.beginCodeTransaction(),
              ),
              const SizedBox(height: 4),
              ItemInfoWidget(
                title: getDerivativesLang(DerivativesKeyLang.closingBalance),
                value: _bloc.endCodeTransaction(),
              ),
              const SizedBox(height: 4),
              ItemInfoWidget(
                title: getDerivativesLang(DerivativesKeyLang.aroseDuringPeriod),
                value: _bloc.getOccurredDuringPeriod(),
                addCharacter: true,
              ),
              const SizedBox(height: 8),
              Expanded(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: buildTransactionView(
                    data: state.listItemStatement ?? [],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget buildTransactionView({required List<DerivativeStatementEntity> data}) {
    return ListViewHelper(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      hasMore: () => false,
      noDataView:
          () => NoneWidget(
            desc: getDerivativesLang(DerivativesKeyLang.noDataNow),
            padding: const EdgeInsets.only(bottom: 20),
          ),
      itemBuilder: (BuildContext context, int index) {
        final item = data[index];
        final visibleDate =
            index == 0 ? true : (item.txdate != data[index - 1].txdate);
        return ItemDerivativeStatementView(
          statement: item,
          visibleDate: visibleDate,
        );
      },
      itemCount: () => data.length,
    );
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }
}
