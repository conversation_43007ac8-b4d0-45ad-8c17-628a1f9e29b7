
import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class DerivativeStatementOfMoneyEmpty extends StatelessWidget {
  const DerivativeStatementOfMoneyEmpty(
      {Key? key,
        this.type = MoneyHistoryEmptyEnum.filterEmpty,
        this.onNavigateToTransfer})
      : super(key: key);
  final MoneyHistoryEmptyEnum type;
  final Function()? onNavigateToTransfer;

  @override
  Widget build(BuildContext context) {
    final isFilter = type == MoneyHistoryEmptyEnum.filterEmpty;
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Column(
        children: [
          const SizedBox(height: 32),
          ConstrainedBox(
              constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.65),
              child: isFilter
                  ? Assets.images.icEmptyFilter.image()
                  : Assets.images.icEmptyDefault.image()),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Text(
              isFilter
                  ? getDerivativesLang(DerivativesKeyLang.noFilter)
                  : getDerivativesLang(DerivativesKeyLang.youDontHaveTransfer),
              style: vpTextStyle.body14?
                  .copyWith(color: themeData.gray500),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),
          isFilter
              ? const SizedBox.shrink()
              : ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: themeData.primary),
              onPressed: () {
                onNavigateToTransfer?.call();
              },
              child: Text(
                  getDerivativesLang(DerivativesKeyLang.transferMoney)))
        ],
      ),
    );
  }
}

enum MoneyHistoryEmptyEnum { defaultEmpty, filterEmpty }
