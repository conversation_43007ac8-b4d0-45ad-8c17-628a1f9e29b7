import 'package:core_ui/core_ui.dart';
import 'package:derivative/common/extensions/derivative_num_extensions.dart';
import 'package:derivative/presentation/derivative_inject_to_stock_app_presentation/derivative_statement/components/info_transaction_bottom_sheet.dart';
import 'package:flutter/material.dart';

import '../../../../shared_widgets/divider_widget.dart';
import '../data/constants/enums.dart';
import '../data/entities/statement_of_money_entities.dart';

class ItemDerivativeStatementView extends StatelessWidget {
  const ItemDerivativeStatementView({
    Key? key,
    required this.statement,
    required this.visibleDate,
  }) : super(key: key);

  final DerivativeStatementEntity statement;
  final bool visibleDate;

  @override
  Widget build(BuildContext context) {
    // Lấy giá trị để hiển thị
    final isCredit = statement.credit! > 0;
    final displayValue = (isCredit ? statement.credit : statement.debit) ?? 0.0;

    final color = isCredit ? ColorUtils.primary : ColorUtils.red;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: visibleDate,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
            width: double.infinity,
            color: ColorUtils.highlightBg,
            child: Text(
              '${statement.txdate}',
              style: TextStyleUtils.text12Weight400.copyWith(
                color: ColorUtils.gray500,
              ),
            ),
          ),
        ),
        Visibility(visible: !visibleDate, child: const Divider3Widget()),
        InkWell(
          onTap:
              () =>
                  showInfoDerivativeTransactionBottomSheet(context, statement),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    getTransactionDescription(statement.tltxcd ?? ''),
                    style: TextStyleUtils.text12Weight500,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  isCredit
                      ? '+${displayValue.toMoney(symbol: 'đ')}'
                      : '-${displayValue.toMoney(symbol: 'đ')}',
                  style: TextStyleUtils.text14Weight600.copyWith(color: color),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
