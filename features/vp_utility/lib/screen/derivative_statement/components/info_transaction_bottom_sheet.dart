import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:core_ui/widgets/design_system/button/button_widget.dart';
import 'package:derivative/common/extensions/derivative_num_extensions.dart';
import 'package:flutter/material.dart';

import '../../../../common/language/key_lang.dart';
import '../../../../common/language/localized_values.dart';
import '../../../../router/router.dart';
import '../../../../shared_widgets/divider_widget.dart';
import '../data/constants/enums.dart';
import '../data/entities/statement_of_money_entities.dart';
import 'base_bottom_sheet.dart';

Future showInfoDerivativeTransactionBottomSheet(
    BuildContext context, DerivativeStatementEntity model) async {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (_) => InfoTransactionBottomSheet(
      model: model,
    ),
  );
}

class InfoTransactionBottomSheet extends StatelessWidget {
  const InfoTransactionBottomSheet({super.key, required this.model});

  final DerivativeStatementEntity model;

  @override
  Widget build(BuildContext context) {
    // Lấy giá trị để hiển thị
    final isCredit = model.credit! > 0;
    final displayValue = (isCredit ? model.credit : model.debit) ?? 0.0;
    final color = isCredit ? ColorUtils.primary : ColorUtils.red;

    return BaseBottomSheet(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 16,
        ),
        item(
          title: getDerivativesLang(DerivativesKeyLang.time),
          value: '${model.txdate}',
        ),
        const SizedBox(
          height: 8,
        ),
        item(
          title: getDerivativesLang(DerivativesKeyLang.tradingName),
          value: getTransactionDescription(model.tltxcd ?? ''),
        ),
        const SizedBox(
          height: 8,
        ),
        const Divider3Widget(),
        const SizedBox(
          height: 8,
        ),
        item(
            title: getDerivativesLang(DerivativesKeyLang.transaction),
            value: isCredit
                ? '+${displayValue.toMoney(symbol: 'đ')}'
                : '-${displayValue.toMoney(symbol: 'đ')}',
            style: TextStyleUtils.text14Weight600.copyWith(color: color)),
        const SizedBox(
          height: 8,
        ),
        item(
          title: getDerivativesLang(DerivativesKeyLang.balanceAfterTransaction),
          value: model.balance?.toMoney(symbol: 'đ') ?? '--đ',
          style: TextStyleUtils.text14Weight600,
        ),
        const SizedBox(
          height: 8,
        ),
        const Divider3Widget(),
        const SizedBox(
          height: 8,
        ),
        Text(
          getDerivativesLang(DerivativesKeyLang.content),
          style: vpTextStyle.body14?.copyWith(
            color: ColorUtils.gray700,
          ),
        ),
        Text(
          '${model.desc}',
          style: TextStyleUtils.text14Weight500,
        ),
        const SizedBox(
          height: 24,
        ),
        ButtonWidget(
          colorBorder: ColorUtils.borderPopUp,
          colorEnable: ColorDefine.transparent,
          textStyle: TextStyleUtils.text14Weight600
              .copyWith(color: ColorUtils.gray700),
          action: getDerivativesLang(DerivativesKeyLang.close),
          onPressed: () => navigation.goBack(),
        ),
      ],
    );
  }

  Widget item(
      {required String title, required String value, TextStyle? style}) {
    return Row(
      children: [
        Text(
          title,
          style: vpTextStyle.body14?.copyWith(
            color: ColorUtils.gray700,
          ),
        ),
        const SizedBox(
          width: 16,
        ),
        Expanded(
          child: Text(
            value,
            style: style ?? vpTextStyle.body14?,
            textAlign: TextAlign.right,
          ),
        )
      ],
    );
  }
}
