import 'package:auto_size_text/auto_size_text.dart';
import 'package:core_ui/core_ui.dart';
import 'package:derivative/presentation/derivative_inject_to_stock_app_presentation/derivative_statment_of_money/components/line_view.dart';
import 'package:flutter/material.dart';

class BuildCreditView extends StatelessWidget {
  const BuildCreditView({
    Key? key,
    required this.title,
    required this.content,
    required this.headerColor,
    required this.backgroundColor,
  }) : super(key: key);

  final String title;

  final String content;

  final Color backgroundColor;

  final Color headerColor;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(8),
          ),
          width: double.infinity,
          padding: const EdgeInsets.all(8),
          child: Column(
            children: [
              Text(
                title,
                style: TextStyleUtils.text12Weight400.copyWith(
                  color: ColorUtils.text500,
                ),
              ),
              const SizedBox(height: 8),
              AutoSizeText(
                content,
                maxLines: 1,
                style: TextStyleUtils.text16Weight500.copyWith(
                  color: ColorUtils.text,
                ),
              ),
            ],
          ),
        ),
        LineView(width: 26, height: 2, borderRadius: 2, color: headerColor),
      ],
    );
  }
}
