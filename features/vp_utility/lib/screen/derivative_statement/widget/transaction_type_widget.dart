import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';

import '../data/constants/transaction_type_value_constant.dart';

class TransactionTypeChips extends StatelessWidget {
  final TransactionType selectedTransactionType;
  final ValueChanged<TransactionType> onTransactionTypeSelected;
  final TextStyle Function(TransactionType) getTransactionTypeLabelStyle;

  const TransactionTypeChips({
    super.key,
    required this.selectedTransactionType,
    required this.onTransactionTypeSelected,
    required this.getTransactionTypeLabelStyle,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children:
            TransactionType.values.map<Widget>((type) {
              return Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: ChoiceChip(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 8,
                  ),
                  label: Text(getDisplayTextForTransactionType(type)),
                  selected: selectedTransactionType == type,
                  onSelected: (bool selected) {
                    if (selected) onTransactionTypeSelected(type);
                  },
                  selectedColor: ColorUtils.primary,
                  backgroundColor: ColorUtils.highlightBg,
                  labelStyle: getTransactionTypeLabelStyle(type),
                ),
              );
            }).toList(),
      ),
    );
  }
}
