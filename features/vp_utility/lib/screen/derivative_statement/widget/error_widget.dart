import 'package:core_ui/core_ui.dart';
import 'package:derivative/presentation/derivative_inject_to_stock_app_presentation/derivative_statment_of_money/widget/bond_button_widget.dart';
import 'package:flutter/material.dart';

import '../../../../common/assets/common_key_assets.dart';
import '../../../../common/error/show_error.dart';
import '../../../../common/language/key_lang.dart';
import '../../../../common/language/localized_values.dart';

class ErrorView extends StatefulWidget {
  const ErrorView({
    Key? key,
    this.error,
    this.responseError,
    this.textButton,
    this.onTryAgain,
    this.margin,
    this.buttonColor,
    this.showErrorImage = false,
  }) : super(key: key);

  final String? error;

  final Object? responseError;

  final String? textButton;

  final VoidCallback? onTryAgain;

  final EdgeInsets? margin;

  final bool showErrorImage;

  final Color? buttonColor;

  @override
  State<ErrorView> createState() => _ErrorViewState();
}

class _ErrorViewState extends State<ErrorView> {
  String errorMessage = '';

  Future<String?>? errorFuture;

  @override
  void initState() {
    super.initState();

    if (widget.responseError != null) {
      errorFuture = getErrorMessage(widget.responseError!);
    }
  }

  @override
  Widget build(BuildContext context) {
    final child = Center(
      child: SizedBox(
        width: double.infinity,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            /// show error image
            if (widget.showErrorImage)
              Image.asset(CommonKeyAssets.error, width: 240),

            /// show content
            if (widget.error != null)
              errorView(widget.error!)
            else if (errorFuture != null)
              FutureBuilder<String?>(
                future: errorFuture,
                builder: (context, snapshot) {
                  return errorView(snapshot.data ?? '');
                },
              ),

            const SizedBox(height: 16),

            /// button try again
            BondButtonWidget(
              color: widget.buttonColor,
              onPressed: widget.onTryAgain,
              title:
                  widget.textButton ??
                  getDerivativesLang(DerivativesKeyLang.retry),
            ),
          ],
        ),
      ),
    );

    if (widget.margin == null) {
      return child;
    }

    return Container(margin: widget.margin, child: child);
  }

  Widget errorView(String error) {
    return Text(
      error,
      textAlign: TextAlign.center,
      style: Theme.of(
        context,
      ).textTheme.bodyLarge!.copyWith(color: ColorUtils.gray500),
    );
  }
}
