import 'dart:ui';

import 'package:core_ui/core_ui.dart';
import 'package:derivative/common/extensions/date_extensions.dart';
import 'package:derivative/presentation/derivative_inject_to_stock_app_presentation/derivative_statement/widget/transaction_type_widget.dart';
import 'package:derivative/presentation/derivative_inject_to_stock_app_presentation/derivative_statment_of_money/widget/bond_bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:tuple/tuple.dart';

import '../../../../common/language/key_lang.dart';
import '../../../../common/language/localized_values.dart';
import '../../../../common/utils/time_utils.dart';
import '../../../../shared_widgets/buttons/double_button_view.dart';
import '../../../../shared_widgets/custom_range_calendar.dart';
import '../components/app_calendar_hint_widget.dart';
import '../components/base_bottom_sheet.dart';
import '../data/constants/transaction_type_value_constant.dart';

class ProfitHistoryFilterView extends StatefulWidget {
  const ProfitHistoryFilterView({
    required this.startDate,
    required this.endDate,
    this.resetStartDate,
    this.resetEndDate,
    this.maxDate,
    required this.initialTransactionType,
    Key? key,
  }) : super(key: key);

  final DateTime startDate;

  final DateTime endDate;

  final DateTime? resetStartDate;

  final DateTime? resetEndDate;

  final DateTime? maxDate;

  final TransactionType initialTransactionType;

  @override
  State<ProfitHistoryFilterView> createState() =>
      _ProfitHistoryFilterViewState();
}

class _ProfitHistoryFilterViewState extends State<ProfitHistoryFilterView> {
  late DateTime stateDate = widget.startDate;
  late DateTime endDate = widget.endDate;

  late TransactionType _selectedTransactionType;

  @override
  void initState() {
    super.initState();
    _selectedTransactionType = widget.initialTransactionType;
  }

  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          getDerivativesLang(DerivativesKeyLang.transaction),
          style: TextStyleUtils.text16Weight500.copyWith(
            color: ColorUtils.text,
          ),
        ),
        const SizedBox(height: 8),
        TransactionTypeChips(
          selectedTransactionType: _selectedTransactionType,
          onTransactionTypeSelected: (type) {
            setState(() {
              _selectedTransactionType = type;
            });
          },
          getTransactionTypeLabelStyle: _getTransactionTypeLabelStyle,
        ),
        const SizedBox(height: 24),
        Text(
          getDerivativesLang(DerivativesKeyLang.time),
          style: TextStyleUtils.text16Weight500.copyWith(
            color: ColorUtils.text,
          ),
        ),
        const SizedBox(height: 10),
        Row(
          children: [
            /// build start date picker
            Expanded(
              child: CalendarWidget(
                onTap: () => showCalendar(),
                text: stateDate.toDate(),
              ),
            ),

            const SizedBox(width: 8),

            Text('-', style: vpTextStyle.body14.copyColor(vpColor.text)),

            const SizedBox(width: 8),

            /// build end date picker
            Expanded(
              child: CalendarWidget(
                onTap: () => showCalendar(),
                text: endDate.toDate(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 25),
        DoubleButtonView(
          leftTitle: getDerivativesLang(DerivativesKeyLang.close),
          rightTitle: getDerivativesLang(DerivativesKeyLang.apply),
          onLeftPressed: () => Navigator.pop(context),
          onRightPressed:
              () => Navigator.pop(
                context,
                Tuple3(stateDate, endDate, _selectedTransactionType),
              ),
        ),
      ],
    );
  }

  void showCalendar() async {
    final data = await showModalBottomSheet(
      barrierColor: ColorUtils.overlayBottomSheet,
      context: context,
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return BondBottomSheet(
          padding: const EdgeInsets.only(top: 16, bottom: 28),
          initialChildSize:
              MediaQuery.of(context).size.height -
              MediaQueryData.fromView(window).padding.top -
              kToolbarHeight,
          child: AppCustomRangeCalendar(
            resetStartDay: widget.resetStartDate,
            resetEndDay: widget.resetEndDate,
            startDay: stateDate,
            endDay: endDate,
            maxDay: widget.maxDate,
            minDay:
                TimeUtils().now.outOfSession() || TimeUtils().now.inBreakTime()
                    ? widget.maxDate?.changeMonth(-36)
                    : widget.maxDate?.changeMonth(-12),
            dateRange: 90,
          ),
        );
      },
    );

    if (data is List<DateTime?> && data.length == 2) {
      stateDate = data.first!;
      endDate = data.last!;
      setState(() {});
    }
  }

  TextStyle _getTransactionTypeLabelStyle(TransactionType type) {
    return _selectedTransactionType == type
        ? TextStyleUtils.text12Weight500.copyWith(color: ColorUtils.textEnable)
        : TextStyleUtils.text12Weight400.copyWith(color: ColorUtils.black);
  }
}
