import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class HomeStockLoading extends StatelessWidget {
  // ignore: prefer_const_constructors_in_immutables
  HomeStockLoading({
    Key? key,
    this.padding = const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
    this.count,
  }) : super(key: key);

  final EdgeInsets padding;
  final int? count;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: count ?? 5,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder:
          (context, index) => Shimmer.fromColors(
            baseColor: ColorUtils.skeletonBase,
            highlightColor: ColorUtils.skeletonHighLight,
            child: Container(
              padding: padding,
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        HighLightLoading(width: 77, height: 20),
                        SizedBox(height: 4),
                        HighLightLoading(width: 164, height: 20),
                      ],
                    ),
                  ),
                  HighLightLoading(width: 77, height: 44),
                ],
              ),
            ),
          ),
      separatorBuilder: (_, index) => Divider(color: ColorUtils.gray100),
    );
  }
}

class HighLightLoading extends StatelessWidget {
  const HighLightLoading({Key? key, required this.width, required this.height})
    : super(key: key);
  final double width;
  final double height;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(SizeUtils.kRadius4),
        color: Theme.of(context).scaffoldBackgroundColor,
      ),
    );
  }
}
