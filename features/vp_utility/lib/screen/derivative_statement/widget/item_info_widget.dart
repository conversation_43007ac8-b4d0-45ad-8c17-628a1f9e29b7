import 'package:core_ui/core_ui.dart';
import 'package:derivative/common/extensions/derivative_num_extensions.dart';
import 'package:flutter/material.dart';

import '../../../../common/extensions/color_exts.dart';

class ItemInfoWidget extends StatelessWidget {
  const ItemInfoWidget({
    super.key,
    required this.title,
    required this.value,
    this.addCharacter = false,
  });

  final String title;
  final num value;
  final bool addCharacter;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(child: Text(title, style: TextStyleUtils.text12Weight500)),
          const SizedBox(width: 16),
          Text(
            value.toMoney(addCharacter: addCharacter, symbol: 'đ'),
            style: TextStyleUtils.text14Weight600.copyWith(
              color: addCharacter ? CommonColorUtils.colorValue(value) : null,
            ),
          ),
        ],
      ),
    );
  }
}
