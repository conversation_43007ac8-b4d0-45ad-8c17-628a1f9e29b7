import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/cubit/recommendation_list/broker_list/broker_list_bloc.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/screen/recommendation_list/broker_list/widget/broker_list_rc.dart';
import 'package:vp_utility/screen/recommendation_list/broker_list/widget/general_rc_tab_view.dart';

class BrokerListScreen extends StatefulWidget {
  const BrokerListScreen({super.key});

  @override
  State<BrokerListScreen> createState() => _BrokerListScreenState();
}

class _BrokerListScreenState extends State<BrokerListScreen>
    with SingleTickerProviderStateMixin {
  late final TabController _controller;

  @override
  void initState() {
    super.initState();
    context.read<BrokerListCubit>().getBrokerRecommendationList();
    _controller = TabController(length: 2, vsync: this, initialIndex: context.read<BrokerListCubit>().state.tabIndex);
    _controller.addListener(() {
      context.read<BrokerListCubit>().init();
      context.read<BrokerListCubit>().selectTab(_controller.index);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return Column(
      children: [
        SizedBox(height: 8),
        Container(
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: colorUtils.highlightBg,
          ),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: BlocBuilder<BrokerListCubit, BrokerListState>(
            buildWhen: (p, c) => p.tabIndex != c.tabIndex,
            builder:
                (context, state) => TabBar(
                  labelStyle: context.textStyle.subtitle14?.copyWith(
                    color: colorUtils.textEnable,
                  ),
                  unselectedLabelStyle: context.textStyle.body14?.copyWith(
                    color: colorUtils.white,
                  ),
                  labelColor: colorUtils.textEnable,
                  unselectedLabelColor: colorUtils.black,
                  padding: EdgeInsets.zero,
                  indicatorSize: TabBarIndicatorSize.tab,
                  controller: _controller,
                  indicator: BoxDecoration(
                    color: colorUtils.primary,
                    borderRadius: BorderRadius.only(
                      topLeft:
                          state.tabIndex == 0
                              ? const Radius.circular(4)
                              : Radius.zero,
                      bottomLeft:
                          state.tabIndex == 0
                              ? const Radius.circular(4)
                              : Radius.zero,
                      topRight:
                          state.tabIndex == 0
                              ? Radius.zero
                              : const Radius.circular(4),
                      bottomRight:
                          state.tabIndex == 0
                              ? Radius.zero
                              : const Radius.circular(4),
                    ),
                  ),

                  tabs: [
                    Tab(child: Text(VpUtilityLocalize.current.rc_stockRc)),
                    Tab(child: Text(VpUtilityLocalize.current.rc_generalRc)),
                  ],
                ),
          ),
        ),
        SizedBox(height: 8),
        Expanded(
          child: TabBarView(
            controller: _controller,
            children: const [BrokerListRC(), GeneralRcTabView()],
          ),
        ),
      ],
    );
  }
}
