import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/cubit/recommendation_list/broker_list/broker_list_bloc.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/response/recommendation/rc_general_model.dart';

class GeneralRcTabView extends StatefulWidget {
  const GeneralRcTabView({super.key});

  @override
  State<GeneralRcTabView> createState() => _GeneralRcTabViewState();
}

class _GeneralRcTabViewState extends State<GeneralRcTabView> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BrokerListCubit, BrokerListState>(
      builder: (context, state) {
        if (state.generalRCList.isEmpty) {
          return NoDataView(content: VpUtilityLocalize.current.rc_no_data);
        }
        return ListView.separated(
          itemBuilder: (_, index) {
            return _itemGeneralRcWidget(state.generalRCList[index], context);
          },
          separatorBuilder:
              (_, index) => const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: DividerWidget(),
              ),

          itemCount: state.generalRCList.length + (state.isLoadingMore ? 1 : 0),
          padding: EdgeInsets.zero,
        );
      },
    );
  }

  Widget _itemGeneralRcWidget(RcGeneralModel generalRC, BuildContext context) {
    final colorUtils = Theme.of(context);
    return InkWell(
      onTap: () => showInfoRcGeneralBottomSheet(context, generalRC),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppTimeUtils.parseStringToString(
                generalRC.createdDate,
                AppTimeUtilsFormat.dateTimeStandard,
                AppTimeUtilsFormat.dateNormal,
              ),
              style: context.textStyle.captionRegular?.copyWith(
                color: colorUtils.gray700,
              ),
            ),
            Text(
              generalRC.title ?? '',
              style: context.textStyle.captionRegular?.copyWith(
                color: colorUtils.gray900,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void showInfoRcGeneralBottomSheet(
    BuildContext context,
    RcGeneralModel generalRC,
  ) {
    VPPopup.bottomSheet(
      ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: 50, // 👈 Chiều cao tối thiểu bạn muốn
          minWidth: MediaQuery.of(context).size.width,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              generalRC.title ?? '',
              style: context.textStyle.subtitle16?.copyWith(
                color: themeData.gray900,
              ),
            ),
            Text(
              AppTimeUtils.parseStringToString(
                generalRC.createdDate,
                AppTimeUtilsFormat.dateWithUtc,
                AppTimeUtilsFormat.dateNormal,
              ),
              style: context.textStyle.captionRegular?.copyWith(
                color: themeData.gray700,
              ),
            ),
            SizedBox(height: 24),
            Text(
              generalRC.detail ?? '',
              style: context.textStyle.body14?.copyWith(
                color: themeData.gray900,
              ),
            ),
          ],
        ),
      ),
    ).showSheet(context);
  }
}
