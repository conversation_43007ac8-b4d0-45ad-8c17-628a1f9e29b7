import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/cubit/recommendation_list/broker_list/broker_list_bloc.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/screen/recommendation_list/broker_list/widget/item_broker_rc_widget.dart';

class BrokerListRC extends StatefulWidget {
  const BrokerListRC({super.key});

  @override
  State<BrokerListRC> createState() => _BrokerListRCState();
}

class _BrokerListRCState extends State<BrokerListRC> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      context.read<BrokerListCubit>().loadMoreData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BrokerListCubit, BrokerListState>(
      builder: (context, state) {
        if (state.brokerRCList.isEmpty && !state.isLoading) {
          return NoDataView(content: VpUtilityLocalize.current.rc_no_data);
        }

        return RefreshIndicator(
          color: vpColor.textBrand,
          onRefresh: () => context.read<BrokerListCubit>().refreshData(),
          child: ListView.separated(
            controller: _scrollController,
            itemBuilder: (_, index) {
              if (index == state.brokerRCList.length) {
                // Show loading indicator at the end
                return state.isLoadingMore
                    ? const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Center(child: VPInnerLoading()),
                    )
                    : const SizedBox.shrink();
              }
              return ItemBrokerRcWidget(data: state.brokerRCList[index]);
            },
            separatorBuilder: (_, index) {
              return const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: DividerWidget(),
              );
            },
            itemCount:
                state.brokerRCList.length + (state.isLoadingMore ? 1 : 0),
            padding: EdgeInsets.zero,
          ),
        );
      },
    );
  }
}
