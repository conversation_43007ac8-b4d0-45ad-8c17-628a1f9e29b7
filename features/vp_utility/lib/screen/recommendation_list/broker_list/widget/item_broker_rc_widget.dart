import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/response/recommendation/broker_recommendation_model.dart';
import 'package:vp_utility/screen/recommendation_home/enum/type_rc_enum.dart';
import 'package:vp_utility/screen/recommendation_home/widget/rc_broker_detail_bottom_sheet.dart';
import 'package:vp_utility/screen/recommendation_list/enum/status_broker_enum.dart';

class ItemBrokerRcWidget extends StatelessWidget {
  final BrokerRecommendationModel data;

  const ItemBrokerRcWidget({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return InkWell(
      onTap: () {
        showModalBottomSheet(
          barrierColor: colorUtils.overlayBottomSheet,
          context: context,
          isDismissible: true,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) {
            return BaseBottomSheet(
              children: [
                RecommendationDetailBottomSheet(
                  stockOrderId: int.parse(data.stockOrderId ?? '0'),
                ),
              ],
            );
          },
        );
      },
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(width: 1, color: colorUtils.divider),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      data.typeRcEnum.localizedTextBroker,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: data.typeRcEnum.color,
                      ),
                    ),
                    SizedBox(width: 12),
                    Container(
                      padding: EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        color: StatusBrokerEnumExt.fromString(
                          data.status ?? '',
                        )?.color(context),
                      ),
                      child: Center(
                        child: Text(
                          StatusBrokerEnumExt.fromString(
                                data.status ?? '',
                              )?.label ??
                              "-",
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4),

                Text(
                  data.symbol ?? '-',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  data.minMaxRecommendedPrice,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                Text(
                  '${VpUtilityLocalize.current.rc_expire}: ${data.expiredDate}',
                  style: context.textStyle.captionRegular?.copyWith(
                    color: colorUtils.gray500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
