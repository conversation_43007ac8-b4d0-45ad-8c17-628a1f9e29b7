import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/cubit/recommendation_list/inv_rc_list/inv_rc_list_cubit.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/response/recommendation/recommendation_info_model.dart';
import 'package:vp_utility/screen/recommendation_home/enum/recommendation_type.dart';
import 'package:vp_utility/screen/recommendation_home/widget/inv_rc_loading.dart';
import 'package:vp_utility/screen/recommendation_list/widget/item_rc_widget.dart';

class ListConsultanRc extends StatefulWidget {
  const ListConsultanRc({super.key});

  @override
  State<ListConsultanRc> createState() => _ListConsultanRcState();
}

class _ListConsultanRcState extends State<ListConsultanRc> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 100) {
      context.read<InvRecommendationListCubit>().loadMoreData(
        isConsultant: true,
      );
    }
  }

  Future<void> _onRefresh() async {
    await context.read<InvRecommendationListCubit>().refreshData(
      RecommendationType.consultingExpert,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<InvRecommendationListCubit, InvRecommendationListState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const InvRcLoading();
        }
        if (state.dataConsultant.isEmpty) {
          return NoDataView(content: VpUtilityLocalize.current.rc_no_data);
        }
        return RefreshIndicator(
          color: vpColor.textBrand,
          onRefresh: _onRefresh,
          child: ListView.separated(
            controller: _scrollController,
            itemBuilder: (_, index) {
              if (index == state.dataConsultant.length) {
                // Show loading indicator at the end
                return state.isLoadingMore
                    ? const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Center(child: VPInnerLoading()),
                    )
                    : const SizedBox.shrink();
              }
              final RecommendationInfoModel model = state.dataConsultant[index];
              bool visibleTime = true;
              if (index > 0 && model.from != null) {
                visibleTime =
                    !model.from!.isSameTime(
                      state.dataConsultant[index - 1].from,
                    );
              }
              return ItemRcWidget(
                model: model,
                visibleTime: visibleTime,
                isNCP: false,
              );
            },
            separatorBuilder: (_, index) {
              if (index == state.dataConsultant.length)
                return const SizedBox.shrink();
              return const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: DividerWidget(),
              );
            },
            itemCount:
                state.dataConsultant.length + (state.isLoadingMore ? 1 : 0),
            padding: EdgeInsets.zero,
          ),
        );
      },
    );
  }
}
