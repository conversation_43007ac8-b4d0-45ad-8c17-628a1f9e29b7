import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/assets.gen.dart';

class UtilitiesItemWidget extends StatelessWidget {
  const UtilitiesItemWidget({super.key, required this.item});

  final UtilsItem item;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: item.onTap,
      child: Stack(
        children: [
          Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: themeData.highlightBg,
            ),
            padding: const EdgeInsets.symmetric(vertical: 16),
            margin: const EdgeInsets.all(8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  item.asset,
                  package: VpUtilityAssets.package,
                  width: 24,
                ),
                SizedBox(height: 8),
                Text(item.label, style: vpTextStyle.body14),
              ],
            ),
          ),
          Positioned(
            right: 0,
            top: 0,
            child: item.badge ?? const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}

class UtilsItem {
  final String asset;
  final String label;
  final String? description;
  final Widget? badge;
  final VoidCallback onTap;

  UtilsItem({
    required this.asset,
    required this.label,
    this.description,
    this.badge,
    required this.onTap,
  });

  static const double itemHeight = 108.0;
}
