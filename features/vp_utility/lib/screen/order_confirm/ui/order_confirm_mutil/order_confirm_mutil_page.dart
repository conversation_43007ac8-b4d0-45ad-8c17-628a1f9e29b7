import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/cubit/order_confirm/bloc/order_confirm_bloc.dart';
import 'package:vp_utility/generated/assets.gen.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_checkbox.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_item.dart';
import 'package:vp_utility/widget/dialog/notifty_dialog.dart';
import 'package:vp_utility/widget/slide_switcher.dart';

import 'order_confirm_mutil_page_bloc.dart';

class OrderConfirmMutilPage extends StatefulWidget {
  const OrderConfirmMutilPage({super.key});

  @override
  State<OrderConfirmMutilPage> createState() => _OrderConfirmMutilPageState();
}

class _OrderConfirmMutilPageState extends State<OrderConfirmMutilPage> {
  OrderConfirmMutilPageBloc bloc = OrderConfirmMutilPageBloc();

  @override
  void dispose() {
    bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: bloc.streamCount,
      builder: (context, snapshot) {
        int select = 0;
        if (snapshot.hasData && snapshot.data is int) {
          select = snapshot.data as int;
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildHeader(context, select),
            buildSelectAll(context),
            buildContent(context),
            SlideSwitcher(
              duration: const Duration(milliseconds: 200),
              begin: const Offset(0, 1),
              show: select > 0,
              child: Container(
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(width: 1, color: themeData.divider),
                  ),
                  color: Theme.of(context).scaffoldBackgroundColor,
                ),
                padding: const EdgeInsets.symmetric(
                  vertical: 16,
                  horizontal: 16,
                ),
                child: VpsButton.custom(
                  title: 'Xác nhận \$ lệnh'.replaceAll('\$', select.toString()),
                  buttonType: ButtonType.primary,
                  onPressed: () => showConfirmOrderMutilDialog(context),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /*--- Build Header ---*/
  Widget buildHeader(BuildContext context, int select) {
    return Container(
      padding: const EdgeInsets.only(right: 16, top: 8, left: 4),
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Row(
        children: [
          IconButton(
            onPressed:
                () => context.read<OrderConfirmBloc>().onChangeMultiSelect(),
            icon: Icon(
              Icons.clear,
              size: 32,
              color: Theme.of(context).focusColor,
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Text(
              "Đã chọn $select",
              style: vpTextStyle.subtitle16.copyColor(themeData.primary),
            ),
          ),
        ],
      ),
    );
  }

  /*--- Build Select All---*/
  Widget buildSelectAll(BuildContext context) {
    final orderConfirmBloc = context.read<OrderConfirmBloc>();

    return Padding(
      padding: const EdgeInsets.only(left: 16),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          BlocBuilder<OrderConfirmBloc, OrderConfirmState>(
            builder: (_, __) {
              return OrderConfirmCheckBox(
                callBackCheck: (status) {
                  orderConfirmBloc.setSelectAll(
                    status == OrderConfirmCheckBoxStatus.allSelected,
                  );
                  bloc.sinkCount(
                    context.read<OrderConfirmBloc>().setIndexSelect.length,
                  );
                },
                status: orderConfirmBloc.selectAllStatus,
              );
            },
          ),
          const SizedBox(width: 16),
          Text('Chọn tất cả'),
        ],
      ),
    );
  }

  /*--- Build Content List  ---*/
  Expanded buildContent(BuildContext context) {
    final bloc = context.read<OrderConfirmBloc>();
    return Expanded(
      child: BlocBuilder<OrderConfirmBloc, OrderConfirmState>(
        builder:
            (_, state) => ListView.separated(
              itemCount: state.listOderConfirm.length,
              shrinkWrap: true,
              separatorBuilder: (_, __) => SizedBox(height: 8),
              itemBuilder:
                  (context, index) => Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: buildItemMutil(
                      index,
                      bloc.setIndexSelect.contains(index),
                      context,
                    ),
                  ),
            ),
      ),
    );
  }

  /*--- Build Item Mutil  ---*/
  Widget buildItemMutil(int index, bool isCheck, BuildContext context) {
    return InkWell(
      onTap: () {},
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Theme.of(context).scaffoldBackgroundColor,
        ),
        child: Row(
          children: [
            OrderConfirmCheckBox(
              callBackCheck: (status) {
                context.read<OrderConfirmBloc>().insertToSetIndexSelect(
                  index,
                  status == OrderConfirmCheckBoxStatus.selected,
                );
                bloc.sinkCount(
                  context.read<OrderConfirmBloc>().setIndexSelect.length,
                );
              },
              status:
                  isCheck
                      ? OrderConfirmCheckBoxStatus.selected
                      : OrderConfirmCheckBoxStatus.unSelected,
              key: UniqueKey(),
            ),
            Expanded(
              child: OrderConfirmItem(
                index: index,
                obj:
                    context
                        .read<OrderConfirmBloc>()
                        .state
                        .listOderConfirm[index],
              ),
            ),
            SizedBox(width: 16),
          ],
        ),
      ),
    );
  }

  /*--- Show dialog mutil select ---*/
  showConfirmOrderMutilDialog(BuildContext context) async {
    bool result = false;
    await showNotifyDialog(
      barrierDismissible: true,
      context: context,
      title: VpUtilityLocalize.current.utility_order_confirm,
      content: VpUtilityLocalize.current.oc_desc_confirm_order,
      image: VpUtilityAssets.icons.icBoardChecked.path,
      textButtonRight: VpUtilityLocalize.current.utility_order_confirm,
      colorButtonRight: themeData.primary,
      textButtonLeft: VpUtilityLocalize.current.oc_close,
      onPressedLeft: () => Navigator.pop(context),
      iconSize: 91,
      onPressedRight: () {
        result = true;
        context.pop(context);
      },
    );
    if (result) {
      if (context.read<OrderConfirmBloc>().setIndexSelect.isEmpty) {
        showSnackBar(
          context,
          VpUtilityLocalize.current.oc_order_no_select,
          isSuccess: false,
        );
        return;
      }
      String orderID = context.read<OrderConfirmBloc>().getOrderIDMutilSelect();
      context.read<OrderConfirmBloc>().checkConfirmOrders(orderID);
    }
  }
}
