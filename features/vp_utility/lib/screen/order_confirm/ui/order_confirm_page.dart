import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/cubit/loading/loading_builder.dart';
import 'package:vp_utility/cubit/order_confirm/bloc/order_confirm_bloc.dart';
import 'package:vp_utility/generated/assets.gen.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/cubit/loading/loading_state.dart';
import 'package:vp_utility/model/mixin/message/message_listener.dart';
import 'package:vp_utility/model/response/order_confirm/enum/order_status.dart';
import 'package:vp_utility/model/response/order_confirm/ext.dart';
import 'package:vp_utility/screen/order_confirm/ui/order_confirm_mutil/order_confirm_mutil_page.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_item.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_loading_widget.dart';
import 'package:vp_utility/widget/app_date_picker.dart';
import 'package:vp_utility/widget/app_lifecycle_listener.dart';
import 'package:vp_utility/widget/button/button_builder.dart';
import 'package:vp_utility/widget/button/button_widget.dart';
import 'package:vp_utility/widget/dialog/popup.dart';
import 'package:vp_utility/widget/dialog/popup_dialog.dart';
import 'package:vp_utility/widget/error_widget.dart';
import 'package:vp_utility/widget/input_filed/input_field_box.dart';
import 'package:vp_utility/widget/none_widget.dart';
import 'package:vp_utility/widget/slide_switcher.dart';

class OrderConfirmPage extends StatefulWidget {
  const OrderConfirmPage({super.key});

  @override
  State<OrderConfirmPage> createState() => _OrderConfirmPageState();
}

class _OrderConfirmPageState extends AppLifeCycleListener<OrderConfirmPage> {
  late OrderConfirmBloc bloc;

  @override
  void initState() {
    super.initState();
    bloc = context.read<OrderConfirmBloc>()..init();
  }

  @override
  void onResumeApp() {
    if (mounted) {
      bloc.onRefresh();
    }
  }

  @override
  Widget build(BuildContext context) => BlocProvider<OrderConfirmBloc>.value(
    value: bloc,
    child: Stack(
      children: [
        Scaffold(
          body: SafeArea(
            child: MessageListener(
              bloc,
              message: {
                'orderConfirmSuccess': (showMultiSelect) {
                  if (showMultiSelect) bloc.onChangeMultiSelect();
                  bloc.onRefresh();
                  // sl.get<HomeOrderConfirmCubit>().onRefresh();
                  showMessage(
                    VpUtilityLocalize.current.oc_oder_confirm_success,
                    isSuccess: true,
                  );
                },
              },
              child: ColoredBox(
                color: themeData.highlightBg,
                child: BlocBuilder<OrderConfirmBloc, OrderConfirmState>(
                  builder:
                      (_, state) =>
                          state.showMultiSelect
                              ? const OrderConfirmMutilPage()
                              : Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  ColoredBox(
                                    color:
                                        Theme.of(
                                          context,
                                        ).scaffoldBackgroundColor,
                                    child: HeaderWidget(
                                      subTitle:
                                          VpUtilityLocalize
                                              .current
                                              .utility_stock,
                                      title:
                                          VpUtilityLocalize
                                              .current
                                              .utility_order_confirm,
                                      actionBack: () => context.pop(),
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  Expanded(child: _Data()),
                                ],
                              ),
                ),
              ),
            ),
          ),
        ),
      ],
    ),
  );

  @override
  void dispose() {
    bloc.close();
    super.dispose();
  }
}

class _Data extends StatefulWidget {
  @override
  __DataState createState() => __DataState();
}

class __DataState extends State<_Data> {
  late final _bloc = context.read<OrderConfirmBloc>();

  showConfirmCommandDialog(String? orderID) => PopupDialog(
        title: VpUtilityLocalize.current.utility_order_confirm,
        content: VpUtilityLocalize.current.oc_desc_confirm_order,
      )
      .icon(VpUtilityAssets.icons.icBoardChecked.path)
      .close
      .button(
        Build.normal(
          label: VpUtilityLocalize.current.utility_order_confirm,
          onPressed: () => context.pop(true),
        ),
      )
      .show(context)
      .then((result) {
        if (result == true) {
          orderID == null
              ? _bloc.checkConfirmAllOrders()
              : _bloc.checkConfirmOrders(orderID);
        }
      });

  /* ---- Nodata---- */
  Widget buildNoneData(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: NoneWidget(
        physics: const AlwaysScrollableScrollPhysics(),
        image: VpUtilityAssets.icons.emptyLayout.path,
        sizeImage: 120,
        isSvgImage: true,
        desc: VpUtilityLocalize.current.oc_no_data,
      ),
    );
  }

  Widget get _bottomButton => Container(
    decoration: BoxDecoration(
      border: Border(top: BorderSide(width: 1, color: themeData.divider)),
      color: Theme.of(context).scaffoldBackgroundColor,
    ),
    padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
    child: ButtonWidget(
      action: VpUtilityLocalize.current.oc_order_confirm_all,
      onPressed: () {
        showConfirmCommandDialog(null);
      },
    ),
  );

  @override
  Widget build(
    BuildContext context,
  ) => BlocBuilder<OrderConfirmBloc, OrderConfirmState>(
    builder:
        (_, state) => Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InputFieldBox(
                    state: _bloc.searchInputState,
                    backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                  ),
                  SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          VpUtilityLocalize.current.oc_sub_account,
                          style: vpTextStyle.subtitle14,
                        ),
                      ),
                      SingleMarkChip<SubAccountType>(
                        data:
                            GetIt.instance<SubAccountCubit>().subAccountsStock
                                .map(
                                  (subAccountsStock) =>
                                      subAccountsStock.toSubAccountType,
                                )
                                .toList(),
                        init: state.subAccountType,
                        label: (item, _) => Center(child: Text(item.label)),
                        spacing: 8,
                        onChange: _bloc.onSubAccountTypeChanged,
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          VpUtilityLocalize.current.oc_status,
                          style: vpTextStyle.subtitle14,
                        ),
                      ),
                      SingleMarkChip<OrderStatus>(
                        data: OrderStatus.values,
                        init: state.orderStatus,
                        spacing: 8,
                        onChange: _bloc.onOrderStatusChanged,
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    VpUtilityLocalize.current.oc_time,
                    style: vpTextStyle.subtitle14,
                  ),
                  SizedBox(height: 4),
                  AppDatePicker(
                    startDate: state.fromDate,
                    endDate: state.toDate,
                    onDateChanged: _bloc.onDateChanged,
                    resetStartDay: DateTime(DateTime.now().year, 1, 1),
                    resetEndDay: DateTime.now(),
                    maxDay: DateTime.now(),
                  ),
                ],
              ),
            ),
            SizedBox(height: 8),
            Expanded(
              child: PullToRefreshView(
                onRefresh: _bloc.onRefresh,
                child: LoadingBuilder(
                  _bloc,
                  retry: ErrorNetworkWidget(
                    onPressed: _bloc.onRefresh,
                    tryAgain: true,
                  ),
                  loading: const OrderConfirmLoadingWidget(),
                  stackLoading: false,
                  child:
                      state.listOderConfirm.isEmpty
                          ? buildNoneData(context)
                          : Column(
                            children: [
                              Expanded(
                                child: ListViewHelper(
                                  itemBuilder:
                                      (_, index) => Padding(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 16,
                                        ),
                                        child: OrderConfirmItem(
                                          index: index,
                                          obj: state.listOderConfirm[index],
                                          onConfirm: showConfirmCommandDialog,
                                          callBack: (index) {
                                            setState(
                                              () => _bloc.resetListWithExpanded(
                                                index,
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                  itemCount: () => state.listOderConfirm.length,
                                  hasMore: () => _bloc.hasMore,
                                  loadMore: _bloc.loadMore,
                                  loadMoreView: () => const VPBankLoading(),
                                  physics:
                                      const AlwaysScrollableScrollPhysics(),
                                ),
                              ),
                            ],
                          ),
                ),
              ),
            ),
            SlideSwitcher(
              duration: const Duration(milliseconds: 200),
              begin: const Offset(0, 1),
              show:
                  state.listOderConfirm.isNotEmpty &&
                  !_bloc.isLoading &&
                  !_bloc.isRetry,
              child: _bottomButton,
            ),
          ],
        ),
  );
}
