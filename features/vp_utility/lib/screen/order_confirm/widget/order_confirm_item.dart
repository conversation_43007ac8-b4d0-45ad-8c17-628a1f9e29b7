import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_number_format_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/cubit/order_confirm/bloc/order_confirm_bloc.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/response/order_confirm/models/order_confirm_obj.dart';
import 'package:vp_utility/screen/recommendation_home/widget/content_expansion_widget.dart';
import 'package:vp_utility/widget/custom_expansion_tile.dart';

import 'order_confirm_item_header.dart';

class OrderConfirmItem extends StatelessWidget {
  const OrderConfirmItem({
    super.key,
    required this.index,
    this.callBack,
    this.onConfirm,
    this.obj,
  });

  final int index;

  final OrderConfirmObj? obj;

  final Function(int)? callBack;

  final Function(String?)? onConfirm;

  @override
  Widget build(BuildContext context) {
    final isMutilSelect =
        context.read<OrderConfirmBloc>().state.showMultiSelect;
    return InkWell(
      onLongPress: () => context.read<OrderConfirmBloc>().onChangeMultiSelect(),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Theme.of(context).scaffoldBackgroundColor,
        ),
        child: CustomExpansionTile(
          key: UniqueKey(),
          initiallyExpanded: obj?.isExpanded ?? false,
          showIconExpand: !isMutilSelect,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          header: Padding(
            padding: EdgeInsets.only(left: isMutilSelect ? 0 : 16),
            child: OrderConfirmItemHeader(index: index, obj: obj),
          ),
          onExpansionChanged: (value) => callBack?.call(index),
          children: [
            isMutilSelect ? const SizedBox.shrink() : buildContent(context),
          ],
        ),
      ),
    );
  }

  /*------ Build content  ------*/
  Widget buildContent(BuildContext context) => Padding(
    padding: const EdgeInsets.symmetric(horizontal: 16),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ContentExpansionWidget(
          title: VpUtilityLocalize.current.oc_sub_account,
          value:
              context
                  .read<OrderConfirmBloc>()
                  .state
                  .subAccountType
                  .toSubAccountModel()
                  ?.afAcctNoExt ??
              '',
        ),
        ContentExpansionWidget(
          title: VpUtilityLocalize.current.oc_command_type,
          value: obj?.pricetype ?? '',
        ),
        ContentExpansionWidget(
          title: VpUtilityLocalize.current.oc_order_time,
          value:
              '${AppTimeUtils.formatTime(obj?.txdate ?? '', format: AppTimeUtilsFormat.dateWithTime)}  ${obj?.txtime ?? ''}',
        ),
        ContentExpansionWidget(
          title: VpUtilityLocalize.current.oc_joint_volume,
          value: AppNumberFormatUtils.shared.percentFormatter.format(
            obj?.orderqtty ?? 0,
          ),
        ),
        ContentExpansionWidget(
          title: VpUtilityLocalize.current.oc_total_money,
          value: obj?.getTotalMoney() ?? '',
        ),
        TextButton(
          onPressed: onConfirm != null ? () => onConfirm!(obj?.orderid) : null,
          child: Text(
            VpUtilityLocalize.current.utility_order_confirm,
            style: vpTextStyle.subtitle14.copyColor(themeData.primary),
          ),
        ),
      ],
    ),
  );
}
