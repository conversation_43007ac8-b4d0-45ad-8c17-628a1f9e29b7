import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_utility/generated/assets.gen.dart';

enum OrderConfirmCheckBoxStatus {
  selected,
  unSelected,
  allSelected,
  unAllSelected,
  continueAllSelected,
}

class OrderConfirmCheckBox extends StatelessWidget {
  const OrderConfirmCheckBox({
    super.key,
    required this.callBackCheck,
    this.status = OrderConfirmCheckBoxStatus.unSelected,
  });

  final Function(OrderConfirmCheckBoxStatus) callBackCheck;
  final OrderConfirmCheckBoxStatus status;

  String get _keyAssets {
    switch (status) {
      case OrderConfirmCheckBoxStatus.selected:
      case OrderConfirmCheckBoxStatus.allSelected:
        return VpUtilityAssets.icons.icCheckboxRect.path;
      case OrderConfirmCheckBoxStatus.continueAllSelected:
        return VpUtilityAssets.icons.icCheckboxContinueRect.path;
      default:
        return VpUtilityAssets.icons.icCheckboxNoneRect.path;
    }
  }

  void onPress() {
    late final OrderConfirmCheckBoxStatus newStatus;
    switch (status) {
      case OrderConfirmCheckBoxStatus.selected:
        newStatus = OrderConfirmCheckBoxStatus.unSelected;
        break;
      case OrderConfirmCheckBoxStatus.unSelected:
        newStatus = OrderConfirmCheckBoxStatus.selected;
        break;
      case OrderConfirmCheckBoxStatus.allSelected:
        newStatus = OrderConfirmCheckBoxStatus.unAllSelected;
        break;
      case OrderConfirmCheckBoxStatus.unAllSelected:
        newStatus = OrderConfirmCheckBoxStatus.allSelected;
        break;
      default:
        newStatus = OrderConfirmCheckBoxStatus.allSelected;
    }
    callBackCheck(newStatus);
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: SvgPicture.asset(
        _keyAssets,
        width: 22,
        package: VpUtilityAssets.package,
      ),
      onPressed: onPress,
    );
  }
}
