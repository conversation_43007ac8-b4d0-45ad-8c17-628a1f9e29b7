import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_number_format_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:flutter/widgets.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/cubit/order_confirm/bloc/order_confirm_bloc.dart';
import 'package:vp_utility/model/response/order_confirm/enum/order_status.dart';
import 'package:vp_utility/model/response/order_confirm/models/order_confirm_obj.dart';

class OrderConfirmItemContent extends StatelessWidget {
  const OrderConfirmItemContent({super.key, required this.index, this.obj});

  final int index;
  final OrderConfirmObj? obj;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                AppTimeUtils.formatTime(
                  obj?.txdate ?? '',
                  format: AppTimeUtilsFormat.dateNormal,
                ),
                style: vpTextStyle.captionMedium.copyColor(themeData.gray700),
              ),
              Text(obj?.symbol ?? '', style: vpTextStyle.subtitle16),
              Text(
                OrderStatus.title(obj?.exectype),
                style: vpTextStyle.captionMedium.copyColor(
                  OrderStatus.parse(obj?.exectype).color,
                ),
              ),
            ],
          ),
        ),
        BlocBuilder<OrderConfirmBloc, OrderConfirmState>(
          builder:
              (context, state) => Text(
                state.subAccountType.toSubAccountModel()?.afAcctNoExt ?? '',
                style: vpTextStyle.subtitle16,
              ),
        ),
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                obj?.getPriceTypeOrPrice() ?? '',
                style: vpTextStyle.subtitle16,
              ),
              Text(
                AppNumberFormatUtils.shared.percentFormatter.format(
                  obj?.orderqtty,
                ),
                style: vpTextStyle.subtitle14.copyColor(themeData.gray700),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
