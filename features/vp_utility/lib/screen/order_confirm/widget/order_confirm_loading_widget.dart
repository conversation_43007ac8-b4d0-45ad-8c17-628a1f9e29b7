import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class OrderConfirmLoadingWidget extends StatelessWidget {
  const OrderConfirmLoadingWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => ListView.separated(
    itemCount: 10,
    shrinkWrap: true,
    physics: const NeverScrollableScrollPhysics(),
    separatorBuilder: (_, __) => SizedBox(height: 8),
    itemBuilder:
        (_, __) => Shimmer.fromColors(
          highlightColor: themeData.skeletonHighLight,
          baseColor: themeData.skeletonBase,
          child: Container(
            padding: const EdgeInsets.all(16),
            child: const Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      HighLightLoading(width: 100, height: 16),
                      SizedBox(height: 4),
                      HighLightLoading(width: 56, height: 24),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      HighLightLoading(width: 100, height: 24),
                      SizedBox(height: 4),
                      HighLightLoading(width: 56, height: 20),
                    ],
                  ),
                ),
                SizedBox(width: 6),
                HighLightLoading(width: 24, height: 24),
              ],
            ),
          ),
        ),
  );
}
