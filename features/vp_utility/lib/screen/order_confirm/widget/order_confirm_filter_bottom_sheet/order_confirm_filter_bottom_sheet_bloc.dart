import 'dart:async';

import 'package:vp_common/vp_common.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_filter_bottom_sheet/helper/data/order_confirm_rangetime_obj.dart';

import 'helper/data/order_confirm_filter_obj.dart';

class OrderConfirmFilterBottomSheetBloc extends BaseStream {
  final objFilter = OrderConfirmFilterObj();

  void fillDataSaved(OrderConfirmFilterObj objSaved) {
    objFilter.itemSelectTime = objSaved.itemSelectTime;
    objFilter.itemSelectedAccount = objSaved.itemSelectedAccount;
    objFilter.itemSelectedTypeOrder = objSaved.itemSelectedTypeOrder;
    objFilter.rangeDateSelected = objSaved.rangeDateSelected;
  }

  final controllerUISelectCalendar = StreamController.broadcast();

  // Rebuild range time
  void rebuilViewWhenSelectCalendar(
    bool isShowRangeView, {
    List<DateTime?>? rangeTime,
  }) {
    OrderConfirmRangeTimeObj objRange = OrderConfirmRangeTimeObj(
      isShowRangeView: isShowRangeView,
      rangeTime: rangeTime,
    );
    controllerUISelectCalendar.sink.add(objRange);
  }

  // Convert tu range Date to string
  String convertRangeDateToString(List<DateTime?>? rangeTime) {
    if (rangeTime == null || rangeTime.length < 2) {
      return '';
    }
    const String dateFormat = AppTimeUtilsFormat.dateWithSimpleYear;
    return '${AppTimeUtils.formatTime(rangeTime[0].toString(), format: dateFormat)} - ${AppTimeUtils.formatTime(rangeTime[1].toString(), format: dateFormat)}';
  }

  @override
  void dispose() {
    controllerUISelectCalendar.close();
  }
}
