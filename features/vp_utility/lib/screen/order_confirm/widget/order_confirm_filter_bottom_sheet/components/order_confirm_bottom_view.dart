import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/widget/button/button_widget.dart';

class DoubleButtonView extends StatelessWidget {
  const DoubleButtonView({
    super.key,
    required this.onLeftPressed,
    required this.onRightPressed,
    this.leftTitle,
    this.rightTitle,
  });

  final Function onLeftPressed;

  final Function onRightPressed;

  final String? leftTitle;

  final String? rightTitle;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ButtonWidget(
            colorBorder: themeData.borderPopUp,
            colorEnable: Colors.transparent,
            textStyle: vpTextStyle.subtitle14.copyColor(themeData.gray700),
            action: leftTitle ?? VpUtilityLocalize.current.oc_reset,
            onPressed: () => onLeftPressed(),
          ),
        ),
        SizedBox(width: 16),
        Expanded(
          child: ButtonWidget(
            action: rightTitle ?? VpUtilityLocalize.current.oc_apply,
            onPressed: () => onRightPressed(),
          ),
        ),
      ],
    );
  }
}
