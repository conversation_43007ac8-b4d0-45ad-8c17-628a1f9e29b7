import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class OrderConfirmSelectView extends StatelessWidget {
  final List<ItemSelect> list;
  final ItemSelect? itemSelected;
  final String title;
  final Function(ItemSelect?) callBack;

  const OrderConfirmSelectView({
    super.key,
    required this.list,
    required this.itemSelected,
    required this.callBack,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    List<ItemSelect> myList = list;

    if (itemSelected != null) {
      int indexSelect = myList.indexWhere(
        (element) => (element.id == itemSelected?.id),
      );
      if (indexSelect >= 0) {
        myList[indexSelect].selected = true;
      }
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: vpTextStyle.subtitle14.copyColor(themeData.gray700)),
        <PERSON><PERSON><PERSON><PERSON>(height: 4),
        ListSelectOne(
          listItem: myList,
          callBackItemSelect: (itemSelect) {
            callBack(itemSelect);
          },
        ),
      ],
    );
  }
}
