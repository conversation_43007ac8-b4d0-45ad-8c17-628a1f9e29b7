import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/assets.gen.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_filter_bottom_sheet/components/order_confirm_bottom_view.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_filter_bottom_sheet/components/order_confirm_select_view.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_filter_bottom_sheet/helper/data/order_confirm_data_select.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_filter_bottom_sheet/helper/data/order_confirm_filter_obj.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_filter_bottom_sheet/helper/data/order_confirm_rangetime_obj.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_filter_bottom_sheet/helper/ui/order_confirm_helper_ui.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_filter_bottom_sheet/order_confirm_filter_bottom_sheet_bloc.dart';
import 'package:vp_utility/widget/app_range_time_view.dart';
import 'package:vp_utility/widget/container_hepler.dart';

class OrderConfirmFilterBottomSheet extends StatefulWidget {
  const OrderConfirmFilterBottomSheet({
    super.key,
    required this.objFilterSaved,
  });

  final OrderConfirmFilterObj objFilterSaved;

  @override
  State<OrderConfirmFilterBottomSheet> createState() =>
      _OrderConfirmFilterBottomSheetState();
}

class _OrderConfirmFilterBottomSheetState
    extends State<OrderConfirmFilterBottomSheet> {
  final bloc = OrderConfirmFilterBottomSheetBloc();
  final dataSelect = OrderConfirmDataSelect();

  @override
  void initState() {
    super.initState();
    bloc.fillDataSaved(widget.objFilterSaved);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 24),
      decoration: ContainerHelper.decorationBottom(),
      child: IntrinsicHeight(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: SvgPicture.asset(
                  VpUtilityAssets.icons.handle.path,
                  package: VpUtilityAssets.package,
                  color: themeData.highLightPopup,
                ),
              ),
              SizedBox(height: 8),
              buildSelectSubAccType(context),
              SizedBox(height: 24),
              buildSelectOrderType(context),
              SizedBox(height: 24),
              buildSelectTimeType(context),
              builTextdRangeTimeSelect(),
              SizedBox(height: 32),
              DoubleButtonView(
                onLeftPressed: () {
                  setState(() {
                    bloc.objFilter.setDefault();
                    bloc.rebuilViewWhenSelectCalendar(false);
                  });
                },
                onRightPressed: () {
                  Navigator.pop(context, bloc.objFilter);
                },
              ),
              SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  /// Select Sub Acc
  OrderConfirmSelectView buildSelectSubAccType(BuildContext context) {
    final listAccount = dataSelect.listAccount();
    return OrderConfirmSelectView(
      title: VpUtilityLocalize.current.oc_sub_account,
      list: listAccount,
      itemSelected: bloc.objFilter.itemSelectedAccount,
      callBack: (itemSelect) {
        bloc.objFilter.itemSelectedAccount = itemSelect;
      },
    );
  }

  /// Select Order Type
  OrderConfirmSelectView buildSelectOrderType(BuildContext context) {
    final listOrderType = dataSelect.listOrderType();
    final buy = VpUtilityLocalize.current.oc_buy;
    final sell = VpUtilityLocalize.current.oc_sell;
    return OrderConfirmSelectView(
      title: '$buy/$sell',
      list: listOrderType,
      itemSelected: bloc.objFilter.itemSelectedTypeOrder,
      callBack: (itemSelect) {
        bloc.objFilter.itemSelectedTypeOrder = itemSelect;
      },
    );
  }

  /// Select Time Type
  Widget buildSelectTimeType(BuildContext context) {
    return StreamBuilder(
      stream: bloc.controllerUISelectCalendar.stream,
      builder: (context, snapshot) {
        final listTime = dataSelect.listTime(bloc.objFilter);
        return OrderConfirmSelectView(
          title: VpUtilityLocalize.current.oc_time,
          list: listTime,
          itemSelected: bloc.objFilter.itemSelectTime,
          callBack: (itemSelect) {
            if (itemSelect != null &&
                itemSelect.id == OrderConfirmFilterTimeType.customM) {
              gotoCalendar();
            } else {
              bloc.rebuilViewWhenSelectCalendar(false);
              bloc.objFilter.setDefaultRangeTime();
              bloc.objFilter.itemSelectTime = itemSelect;
            }
          },
        );
      },
    );
  }

  /// Goto Calendar
  void gotoCalendar() {
    final offset =
        MediaQuery.of(context).size.height -
        MediaQueryData.fromView(window).padding.top -
        kToolbarHeight;
    OrderConfirmHelperUI.showCalendar(
      context,
      initialChildSize: offset,
      startDate: bloc.objFilter.getStartDate(),
      endDate: bloc.objFilter.getEndDate(),
      callBack: (list) {
        handleCallBackSelectCalendar(list);
      },
    );
  }

  /// Handler call back from calendar
  void handleCallBackSelectCalendar(List<dynamic> list) {
    bloc.objFilter.rangeDateSelected = [
      list[0] ?? DateTime.now(),
      list[1] ?? DateTime.now(),
    ];
    bloc.objFilter.itemSelectTime = ItemSelect(
      title: bloc.objFilter.getMonth(OrderConfirmFilterTimeType.customM),
      id: OrderConfirmFilterTimeType.customM,
      unAutoSelected: true,
    );
    if (list.isNotEmpty) {
      Future.delayed(const Duration(milliseconds: 200), () {
        bloc.rebuilViewWhenSelectCalendar(
          true,
          rangeTime: bloc.objFilter.rangeDateSelected,
        );
      });
    }
  }

  /// Time range select
  Widget builTextdRangeTimeSelect() {
    return StreamBuilder(
      stream: bloc.controllerUISelectCalendar.stream,
      builder: (context, snapshot) {
        bool isShowRangeView = false;
        String rangeDate = '';
        if (!snapshot.hasData) {
          isShowRangeView =
              bloc.objFilter.itemSelectTime != null &&
              bloc.objFilter.itemSelectTime?.id ==
                  OrderConfirmFilterTimeType.customM;
          if (bloc.objFilter.rangeDateSelected.isNotEmpty) {
            rangeDate = bloc.convertRangeDateToString(
              bloc.objFilter.rangeDateSelected,
            );
          }
        }
        if (snapshot.hasData && snapshot.data is OrderConfirmRangeTimeObj) {
          final obj = snapshot.data as OrderConfirmRangeTimeObj;
          isShowRangeView = obj.isShowRangeView ?? false;
          rangeDate = bloc.convertRangeDateToString(obj.rangeTime);
        }
        return isShowRangeView
            ? InkWell(
              onTap: () {
                gotoCalendar();
              },
              child: Column(
                children: [
                  SizedBox(height: 16),
                  AppRangeTimeView(value: rangeDate),
                ],
              ),
            )
            : const SizedBox.shrink();
      },
    );
  }
}
