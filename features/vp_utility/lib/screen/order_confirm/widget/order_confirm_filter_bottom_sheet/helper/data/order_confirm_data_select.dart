import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/response/order_confirm/enum/common_enum.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_filter_bottom_sheet/helper/data/order_confirm_filter_obj.dart';

class OrderConfirmDataSelect {
  late BuildContext? context;

  OrderConfirmDataSelect() {
    context = getContext;
  }

  listAccount() {
    if (context == null) {
      return [];
    }
    return [
      ItemSelect(
        title: VpUtilityLocalize.current.oc_normal,
        id: CommonAccountType.normal,
      ),
      ItemSelect(
        title: VpUtilityLocalize.current.oc_margin,
        id: CommonAccountType.margin,
      ),
    ];
  }

  listOrderType() {
    if (context == null) {
      return [];
    }
    return [
      ItemSelect(
        title: VpUtilityLocalize.current.oc_all,
        id: OrderConfirmFiterOrderType.all,
      ),
      ItemSelect(
        title: VpUtilityLocalize.current.oc_buy_order,
        id: OrderConfirmFiterOrderType.buy,
      ),
      ItemSelect(
        title: VpUtilityLocalize.current.oc_sell_order,
        id: OrderConfirmFiterOrderType.sell,
      ),
    ];
  }

  listTime(OrderConfirmFilterObj objFilter) {
    if (context == null) {
      return [];
    }
    return [
      ItemSelect(
        title: objFilter.getMonth(OrderConfirmFilterTimeType.oneM),
        id: OrderConfirmFilterTimeType.oneM,
      ),
      ItemSelect(
        title: objFilter.getMonth(OrderConfirmFilterTimeType.twoM),
        id: OrderConfirmFilterTimeType.twoM,
      ),
      ItemSelect(
        title: objFilter.getMonth(OrderConfirmFilterTimeType.threeM),
        id: OrderConfirmFilterTimeType.threeM,
      ),
      ItemSelect(
        title: objFilter.getMonth(OrderConfirmFilterTimeType.customM),
        id: OrderConfirmFilterTimeType.customM,
        unAutoSelected: true,
      ),
    ];
  }
}
