import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/response/order_confirm/enum/common_enum.dart';
import 'package:vp_utility/screen/order_confirm/widget/order_confirm_filter_bottom_sheet/helper/data/order_confirm_rangetime_obj.dart';

class OrderConfirmFilterObj {
  ItemSelect? itemSelectedAccount;
  ItemSelect? itemSelectedTypeOrder;
  ItemSelect? itemSelectTime;
  List<DateTime?> rangeDateSelected = [];

  OrderConfirmFilterObj() {
    setDefault();
  }

  void setDefault() {
    final subAcc = GetIt.instance<SubAccountCubit>().defaultSubAccount;
    itemSelectedAccount = ItemSelect(
      title:
          subAcc.isNormal
              ? VpUtilityLocalize.current.oc_normal
              : VpUtilityLocalize.current.oc_margin,
      id: subAcc.isNormal ? CommonAccountType.normal : CommonAccountType.margin,
    );
    itemSelectedTypeOrder = ItemSelect(
      title: VpUtilityLocalize.current.oc_all,
      id: OrderConfirmFiterOrderType.all,
    );
    itemSelectTime = ItemSelect(
      title: getMonth(OrderConfirmFilterTimeType.threeM),
      id: OrderConfirmFilterTimeType.threeM,
    );
  }

  //  check defaulrt
  bool isValueDefault() {
    final id =
        GetIt.instance<SubAccountCubit>().defaultSubAccount.isNormal
            ? CommonAccountType.normal
            : CommonAccountType.margin;
    return (itemSelectedAccount?.id == id) &&
        (itemSelectedTypeOrder?.id == OrderConfirmFiterOrderType.all) &&
        (itemSelectTime?.id == OrderConfirmFilterTimeType.threeM);
  }

  String getMonth(OrderConfirmFilterTimeType typeM) {
    final month = VpUtilityLocalize.current.oc_month;
    final custom = VpUtilityLocalize.current.oc_custom;
    final mapTime = {
      OrderConfirmFilterTimeType.oneM: '1 $month',
      OrderConfirmFilterTimeType.twoM: '2 $month',
      OrderConfirmFilterTimeType.threeM: '3 $month',
      OrderConfirmFilterTimeType.customM: custom,
    };
    return mapTime[typeM] ?? '--';
  }

  // get startDate
  DateTime getStartDate() {
    return rangeDateSelected.length >= 2
        ? rangeDateSelected[0] ?? OrderConfirmRangeTimeObj.preThreeMTime()
        : OrderConfirmRangeTimeObj.preThreeMTime();
  }

  // get end date
  DateTime getEndDate() {
    return rangeDateSelected.length >= 2
        ? rangeDateSelected[1] ?? DateTime.now()
        : DateTime.now();
  }

  // Clear rangeDate
  void setDefaultRangeTime() {
    final now = DateTime.now();
    rangeDateSelected = [DateTime(now.year, now.month - 3, now.day), now];
  }
}

/// Enum
enum OrderConfirmFiterOrderType { all, sell, buy }

enum OrderConfirmFilterTimeType { oneM, twoM, threeM, customM }
