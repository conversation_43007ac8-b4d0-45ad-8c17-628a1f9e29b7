import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/widget/app_custom_range_calendar.dart';
import 'package:vp_utility/widget/bottom_sheet/order_confirm_bottom_sheet.dart';

class OrderConfirmHelperUI {
  static void showCalendar(
    BuildContext context, {
    double? initialChildSize,
    DateTime? startDate,
    DateTime? endDate,
    required Function(List<dynamic>) callBack,
  }) async {
    await showModalBottomSheet(
      barrierColor: themeData.overlayBottomSheet,
      context: context,
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: const Color.fromRGBO(48, 21, 21, 0),
      builder: (context) {
        return OrderConfirmBottomSheet(
          padding: const EdgeInsets.only(top: 16, bottom: 28),
          initialChildSize: initialChildSize,
          child: AppCustomRangeCalendar(
            minDay: DateTime(1950, 1, 1),
            maxDay: DateTime(2100, 1, 1),
            startDay: startDate,
            endDay: endDate,
            dateRange: 90,
          ),
        );
      },
    ).then((param) {
      if (param is List && param.isNotEmpty) {
        callBack(param);
      }
    });
  }
}
