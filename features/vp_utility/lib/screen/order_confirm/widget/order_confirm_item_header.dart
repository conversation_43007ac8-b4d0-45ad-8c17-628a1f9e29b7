import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vp_utility/model/response/order_confirm/models/order_confirm_obj.dart';

import 'order_confirm_item_content.dart';

class OrderConfirmItemHeader extends StatelessWidget {
  const OrderConfirmItemHeader({required this.index, this.obj, super.key});

  final int index;

  final OrderConfirmObj? obj;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: OrderConfirmItemContent(index: index, obj: obj),
    );
  }
}
