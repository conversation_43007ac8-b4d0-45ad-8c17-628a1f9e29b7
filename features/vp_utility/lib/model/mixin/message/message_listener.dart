import 'package:flutter/material.dart';
import 'message.dart';

class MessageListener extends StatelessWidget {
  final Map<dynamic, Function(dynamic content)> message;
  final MessageState bloc;
  final Widget? child;

  const MessageListener(
    this.bloc, {
    super.key,
    required this.message,
    this.child,
  });

  @override
  Widget build(BuildContext context) => ValueListenableBuilder<Message?>(
        valueListenable: bloc.messageListenable,
        builder: (_, value, child) {
          if (value != null && message.keys.contains(value.type)) {
            message[value.type]?.call(value.content);
          }
          return child ?? SizedBox.shrink();
        },
        child: child,
      );
}
