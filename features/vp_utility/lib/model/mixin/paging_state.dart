import 'package:tuple/tuple.dart';

mixin PagingState<T> {
  late List<T> _data = [];
  late int _pageOffset;
  int _totalElements = -1;
  late Future<Tuple3<List<T>, int, int>> Function(int pageOffset) _callData;
  late Function(List<T> data) _onData;

  int get itemCount => _data.length;

  bool get hasMore => _totalElements == -1 || itemCount < _totalElements;

  Future<void> _paging(
    Future<Tuple3<List<T>, int, int>> Function(int pageOffset) callData,
    Function(List<T> data) onData,
    int initOffset,
    bool init,
  ) async {
    final result = await callData(init ? initOffset : _pageOffset + 1);
    _data = [...(init ? <T>[] : _data), ...result.item1];
    _pageOffset = result.item2;
    _totalElements = result.item3;
    onData(_data);
    if (init) {
      _callData = callData;
      _onData = onData;
    }
  }

  Future<void> paging(
    Future<Tuple3<List<T>, int, int>> Function(int pageOffset) callData, {
    required Function(List<T> data) onData,
    int initOffset = 1,
  }) =>
      _paging(callData, onData, initOffset, true);

  Future<void> more() => _paging(_callData, _onData, -1, false);
}
