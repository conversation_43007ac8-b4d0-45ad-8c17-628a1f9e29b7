import 'dart:math';

import 'package:flutter/services.dart';

mixin InputFormatter {
  List<TextInputFormatter> get inputFormatters => [];

  TextInputFormatter get trim => TextInputFormatter.withFunction(
        (oldValue, newValue) {
          final newText = newValue.text.trim();
          return TextEditingValue(
            text: newText,
            selection:
                TextSelection.collapsed(offset: oldValue.offsetWith(newText)),
          );
        },
      );

  TextInputFormatter get thousand => TextInputFormatter.withFunction(
        (oldValue, newValue) {
          final newText = newValue.text
              .replaceAll(RegExp(r'\D'), '')
              .replaceAll(RegExp(r'^0+'), '')
              .replaceAll(RegExp(r'\B(?=(\d{3})+(?!\d))'), ',');
          int offset = oldValue.offsetWith(newText);
          if (offset > 0 && newText[offset - 1] == ',') offset--;
          return TextEditingValue(
            text: newText,
            selection: TextSelection.collapsed(offset: offset),
          );
        },
      );

  TextInputFormatter get price => TextInputFormatter.withFunction(
        (oldValue, newValue) {
          final newText = newValue.text
              .replaceAll(',', '.')
              .replaceAll(RegExp(r'^0+'), '0')
              .replaceAll(RegExp(r'^\.+'), '0.')
              .replaceAll(RegExp(r'^(?=0[1-9])0|[^\d.]'), '');
          if (!RegExp(r'^\d*\.?\d{0,2}$').hasMatch(newText)) {
            return oldValue;
          }
          final offset = oldValue.offsetEnd == 0
              ? newText.length
              : newValue.offsetWith(newText);
          return TextEditingValue(
            text: newText,
            selection: TextSelection.collapsed(offset: offset),
          );
        },
      );

  TextInputFormatter maxLength([int maxLength = 8]) =>
      TextInputFormatter.withFunction(
        (oldValue, newValue) =>
            newValue.text.length <= maxLength ? newValue : oldValue,
      );
}

extension TextEditingValueExt on TextEditingValue {
  int get offsetEnd => text.length - selection.end;

  int offsetWith(String text) =>
      (text.length - offsetEnd).clamp(0, text.length);
}

extension DoubleExt on double {
  double add(double value, {required bool increase, int fixed = 2}) {
    if (this <= value && !increase) return 0.0;
    final round = pow(10, (fixed - 1).clamp(0, 5)) ~/ value;
    return ((this * round).round() +
            (increase ? 1 : -1) * (value * round).round()) /
        round;
  }
}
