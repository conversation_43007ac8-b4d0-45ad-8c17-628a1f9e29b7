import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

enum OrderStatus {
  all,
  buy,
  sell;

  String get api {
    switch (this) {
      case buy:
        return 'NB,AB,CB';
      case sell:
        return 'NS,AS,CS';
      default:
        return '';
    }
  }

  factory OrderStatus.parse(String? text) =>
      (text?.toUpperCase().contains('MUA') ?? false) ? buy : sell;

  static String title(String? text) =>
      text == 'Mua' || text == 'Bán'
          ? OrderStatus.parse(text).toString()
          : text ?? '';

  Color get color => this == buy ? themeData.primary : themeData.red;

  @override
  String toString() {
    switch (this) {
      case OrderStatus.buy:
        return 'Lệnh mua';
      case OrderStatus.sell:
        return 'Lệnh bán';
      default:
        return 'Tất cả';
    }
  }
}
