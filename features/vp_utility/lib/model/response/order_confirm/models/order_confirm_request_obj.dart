class OrderConfirmRequestObj {
  String? authType;
  String? tokenid;
  String? transactionId;
  String? orderId;

  OrderConfirmRequestObj(
      {this.authType, this.tokenid, this.transactionId, this.orderId});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['authType'] = authType;
    data['tokenid'] = tokenid;
    data['transactionId'] = transactionId;
    data['orderId'] = orderId;
    return data;
  }
}
