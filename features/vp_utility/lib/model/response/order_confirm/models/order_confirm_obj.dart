import 'package:vp_common/utils/app_number_format_utils.dart';
import 'package:vp_common/vp_common.dart';

class OrderConfirmObj {
  String? orderid;
  String? txdate;
  String? codeid;
  String? tradeplace;
  String? exectype;
  String? exectypeEn;
  String? odexectype;
  String? pricetype;
  String? via;
  num? orderqtty; // Khoi luong khop
  num? quoteprice; // Gia khop
  String? reforderid;
  String? symbol;
  String? confirmed;
  String? afacctno;
  String? custodycd;
  String? fullname;
  String? rootorderid;
  String? txtime;
  num? execqtty;
  num? execamt;
  num? totalno;

  bool isExpanded = false;

  OrderConfirmObj({
    this.orderid,
    this.txdate,
    this.codeid,
    this.tradeplace,
    this.exectype,
    this.exectypeEn,
    this.odexectype,
    this.pricetype,
    this.via,
    this.orderqtty,
    this.quoteprice,
    this.reforderid,
    this.symbol,
    this.confirmed,
    this.afacctno,
    this.custodycd,
    this.fullname,
    this.rootorderid,
    this.txtime,
    this.execqtty,
    this.execamt,
    this.totalno,
  });

  OrderConfirmObj.fromJson(Map<String, dynamic> json) {
    orderid = json['orderid'];
    txdate = json['txdate'];
    codeid = json['codeid'];
    tradeplace = json['tradeplace'];
    exectype = json['exectype'];
    exectypeEn = json['exectype_en'];
    odexectype = json['odexectype'];
    pricetype = json['pricetype'];
    via = json['via'];
    orderqtty = json['orderqtty'];
    quoteprice = json['quoteprice'];
    reforderid = json['reforderid'];
    symbol = json['symbol'];
    confirmed = json['confirmed'];
    afacctno = json['afacctno'];
    custodycd = json['custodycd'];
    fullname = json['fullname'];
    rootorderid = json['rootorderid'];
    txtime = json['txtime'];
    execqtty = json['execqtty'];
    execamt = json['execamt'];
    totalno = json['totalNo'];
  }

  // Gia co phieu
  String? getQuoteprice() {
    return quoteprice != null
        ? AppNumberFormatUtils.shared.percentFormatter.format(
          (quoteprice ?? 0) / 1000,
        )
        : '';
  }

  // Tong tien
  String getTotalMoney() {
    if (orderqtty == null || quoteprice == null) {
      return '';
    }
    return (orderqtty! * quoteprice!).toMoney();
  }

  // Xu ly mang, loai bo cac loai lenh cu
  static List<OrderConfirmObj> transformList(List<OrderConfirmObj> data) {
    List<String> listOldOrderID = [];
    List<OrderConfirmObj> listData = data;
    for (var item in data) {
      if (item.reforderid != null && (item.reforderid ?? '').isNotEmpty) {
        final index = data.indexWhere((element) {
          return element.orderid == item.reforderid;
        });
        if (index >= 0) {
          listOldOrderID.add(data[index].orderid ?? '');
        }
      }
    }
    if (listOldOrderID.isNotEmpty) {
      for (var oldOrderID in listOldOrderID) {
        listData.removeWhere((element) {
          return element.orderid == oldOrderID;
        });
      }
    }

    return listData;
  }

  // Lay thong tin hien thi loai lenh hoac gia
  String getPriceTypeOrPrice() {
    final vPriceType = (pricetype ?? '').toLowerCase();
    final vPrice = (quoteprice ?? 0).toMoney();
    if ((vPriceType == OrderType.lo.name || vPriceType == OrderType.gtc.name)) {
      return vPrice;
    }
    return (pricetype ?? '');
  }
}

enum OrderType {
  lo,
  buyIn,
  stopLoss,
  takeProfit,
  waiting,
  gtc;

  bool get isLo => this == OrderType.lo;

  bool get isBuyIn => this == OrderType.buyIn;

  bool get isGtc => this == OrderType.gtc;

  bool get isLoOrGtc => isLo || isGtc;

  bool get isLoOrBuyIn => isLo || isBuyIn;
}
