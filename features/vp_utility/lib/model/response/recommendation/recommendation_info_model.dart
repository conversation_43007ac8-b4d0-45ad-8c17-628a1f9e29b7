import 'package:json_annotation/json_annotation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/screen/recommendation_home/enum/type_rc_enum.dart';

part 'recommendation_info_model.g.dart';

@JsonSerializable()
class RecommendationInfoModel {
  final String? id;
  final String? symbol;
  final String? stockType;
  final String? method;
  final String? typeRc;
  final double? expectPriceBmin;
  final double? expectPriceBmax;
  final double? targetPrice;
  final double? stopLossPrice;
  final String? investmentHorizon;
  final String? statusRc;
  final String? createdAt;
  final String? acceptedAt;
  final String? updatedAt;
  final String? fromDate;
  final String? toDate;
  final String? reasonLink;
  final String? reasonTxt;
  final String? createdBy;
  final String? acceptedBy;
  final String? updatedBy;
  final String? title;
  final String? shortDescription;
  final String? descriptionHtml;
  final String? oe;
  final String? exchangeCode;
  final String? investTime;

  RecommendationInfoModel({
    this.id,
    this.symbol,
    this.stockType,
    this.method,
    this.typeRc,
    this.expectPriceBmin,
    this.expectPriceBmax,
    this.targetPrice,
    this.stopLossPrice,
    this.investmentHorizon,
    this.statusRc,
    this.createdAt,
    this.acceptedAt,
    this.updatedAt,
    this.fromDate,
    this.toDate,
    this.reasonLink,
    this.reasonTxt,
    this.createdBy,
    this.acceptedBy,
    this.updatedBy,
    this.title,
    this.shortDescription,
    this.descriptionHtml,
    this.oe,
    this.exchangeCode,
    this.investTime,
  });

  factory RecommendationInfoModel.fromJson(Map<String, dynamic> json) =>
      _$RecommendationInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$RecommendationInfoModelToJson(this);
}

extension RecommendationInfoModelExts on RecommendationInfoModel {
  String getValuePrice(num? price, {String? empty}) =>
      price != null
          ? price.toDouble().getPriceFormatted(convertToThousand: true)
          : empty ?? '';
  bool get active => (statusRc ?? '').toUpperCase() == "ACTIVE";

  TypeRcEnum get typeRcEnum => TypeRcEnumExtension.parseTypeRcEnum(typeRc);

  String get getMethod {
    switch (method ?? ''.toUpperCase()) {
      case 'BASIC':
        return VpUtilityLocalize.current.rc_basic;
      case 'TECHNIQUE':
        return VpUtilityLocalize.current.rc_technique;
      case 'VALMO':
        return VpUtilityLocalize.current.rc_valmo;
      case 'EXMO':
        return VpUtilityLocalize.current.rc_exmo;
      case 'SECMO':
        return VpUtilityLocalize.current.rc_secmo;
      case 'SMCMO':
        return VpUtilityLocalize.current.rc_smcmo;
      default:
        return '';
    }
  }

  DateTime? get from => AppTimeUtils.parseDateTimeString(
    fromDate,
    format: AppTimeUtilsFormat.dateYMD,
  );

  String get invTime {
    switch (investTime) {
      case 'W':
        return VpUtilityLocalize.current.rc_w;
      case 'D':
        return VpUtilityLocalize.current.rc_d;
      case 'M':
        return VpUtilityLocalize.current.rc_m;
      case 'O1MU3M':
        return VpUtilityLocalize.current.rc_o1mu3m;
      case 'O3MU6M':
        return VpUtilityLocalize.current.rc_o3mu6m;
      case 'O6MU1Y':
        return VpUtilityLocalize.current.rc_o6mu1y;
      case 'O1Y':
        return VpUtilityLocalize.current.rc_o1y;
      default:
        return '';
    }
  }
}
