import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart' hide AppLocalizationDelegate;
import 'package:vp_core/vp_core.dart';
import 'package:vp_utility/core/repository/order_confirm/order_confirm_repository.dart';
import 'package:vp_utility/core/repository/order_confirm/order_confirm_repository_impl.dart';
import 'package:vp_utility/core/repository/recommendation_repository.dart';
import 'package:vp_utility/core/service/recommendation_service.dart';
import 'package:vp_utility/cubit/order_confirm/bloc/order_confirm_bloc.dart';
import 'package:vp_utility/generated/intl/messages_all.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/router/vp_utility_router.dart';
import 'package:vp_utility/screen/order_confirm/ui/order_confirm_page.dart';
import 'package:vp_utility/screen/recommendation_home/enum/recommendation_type.dart';
import 'package:vp_utility/screen/recommendation_list/recommendation_list.dart';
import 'package:vp_utility/screen/utils_page/utilities_page.dart';

class VpUtilityModule implements Module {
  @override
  void injectServices(GetIt service) {
    service.registerFactory<OrderConfirmRepository>(
      () => OrderConfirmRepositoryImpl(restClient: service()),
    );
    service.registerLazySingleton(
      () => RecommendationService(
        service(),
        // todo: sửa lại endpoint
        baseUrl: "https://neopro-uat.vpbanks.com.vn",
      ),
    );
    service.registerLazySingleton<RecommendationRepository>(
      () => RecommendationRepositoryImpl(recommendationService: service()),
    );
  }

  @override
  List<RouteBase> router() {
    return [
      GoRoute(
        path: VpUtilityRouter.recommendationList.routeName,
        name: VpUtilityRouter.recommendationList.routeName,
        builder:
            (context, state) => RecommendationList(
              recommendationType: state.extra as RecommendationType?,
            ),
      ),
      GoRoute(
        path: VpUtilityRouter.securitiesStatement.routeName,
        name: VpUtilityRouter.securitiesStatement.routeName,
        builder:
            (context, state) => RecommendationList(
              recommendationType: state.extra as RecommendationType?,
            ),
      ),
      GoRoute(
        path: VpUtilityRouter.orderConfirm.routeName,
        name: VpUtilityRouter.orderConfirm.routeName,
        builder: (context, state) {
          return MultiBlocProvider(
            providers: [
              BlocProvider(create: (_) => OrderConfirmBloc()),
              // BlocProvider.value(
              //   value: GetIt.instance.get<HomeOrderConfirmCubit>(),
              // ),
            ],
            child: const OrderConfirmPage(),
          );
        },
      ),
    ];
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [MultiLocalizationsDelegate.delegate];
  }

  @override
  String modulePath() {
    return "vpUtility";
  }
}

class MultiLocalizationsDelegate extends AppLocalizationDelegate {
  const MultiLocalizationsDelegate();

  static const AppLocalizationDelegate delegate = MultiLocalizationsDelegate();

  @override
  Future<VpUtilityLocalize> load(Locale locale) {
    return MultipleLocalizations.load(
      initializeMessages,
      locale,
      (l) => VpUtilityLocalize.load(locale),
      setDefaultLocale: true,
    );
  }
}
