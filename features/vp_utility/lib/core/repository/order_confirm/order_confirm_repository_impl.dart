import 'package:tuple/tuple.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_common/widget/log_api/models/app_base_response.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_utility/core/constant/order_confirm_path_api.dart';
import 'package:vp_utility/model/response/check_model.dart';
import 'package:vp_utility/model/response/order_confirm/models/order_confirm_obj.dart';
import 'package:vp_utility/model/response/order_confirm/models/order_confirm_request_obj.dart';
import 'package:vp_utility/utils/utilities_utils.dart';
import 'order_confirm_repository.dart';

class OrderConfirmRepositoryImpl extends OrderConfirmRepository {
  final Dio _restClient;

  OrderConfirmRepositoryImpl({required Dio restClient})
    : _restClient = restClient;

  /* API lay danh sach xac nhan lenh */
  @override
  Future<Tuple3<List<OrderConfirmObj>, int, int>> confirmOrders({
    String? subAccount,
    String? fromDate,
    String? toDate,
    String? execType,
    String? symbol,
    required int pageIndex,
    required int pageSize,
  }) async {
    List<OrderConfirmObj> list = [];
    int totalElements = 0;
    try {
      Response response = await _restClient.get(
        OrderConfirmPathApi.confirmOrders(
          subAccount: subAccount,
          fromDate: fromDate,
          toDate: toDate,
          execType: execType,
          symbol: symbol,
          pageIndex: pageIndex,
          pageSize: pageSize,
        ),
      );
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data is List) {
        for (var jsonObj in result.data) {
          OrderConfirmObj obj = OrderConfirmObj.fromJson(jsonObj);
          list.add(obj);
        }
        totalElements = list.isEmpty ? 0 : list.first.totalno?.toInt() ?? 0;
        // list = OrderConfirmObj.transformList(list);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return Tuple3(list, pageIndex, totalElements);
  }

  @override
  Future<int> confirmOrdersInfor({String? subAccount}) async {
    try {
      Response response = await _restClient.get(
        OrderConfirmPathApi.confirmOrdersInfor(subAccount: subAccount),
      );

      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data is List) {
        if ((result.data as List).isEmpty) return 0;
        return result.data.first['cnt'];
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }

  /* API kiem tra xac nhan lenh */
  final kOrderID = 'orderId';

  @override
  Future<CheckModel> checkConfirmOrders({
    String? subAccount,
    String? orderID,
  }) async {
    try {
      Response response = await _restClient.post(
        OrderConfirmPathApi.checkConfirmOrders(
          subAccount: subAccount,
          orderId: orderID,
        ),
        data: {kOrderID: orderID},
      );

      AppBaseResponse appBaseObj = AppBaseResponse.fromJson(response.data);

      if (appBaseObj.isSuccess() && appBaseObj.data != null) {
        return CheckModel.fromJson(appBaseObj.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<CheckModel> checkConfirmAllOrders({String? subAccount}) async {
    try {
      Response response = await _restClient.post(
        OrderConfirmPathApi.checkConfirmAllOrders(subAccount: subAccount),
      );

      AppBaseResponse appBaseObj = AppBaseResponse.fromJson(response.data);

      if (appBaseObj.isSuccess() && appBaseObj.data != null) {
        return CheckModel.fromJson(appBaseObj.data);
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }

  /* API xac nhan lenh */
  @override
  Future<AppBaseResponse> transConfirmOrders({
    String? subAccount,
    OrderConfirmRequestObj? param,
  }) async {
    try {
      Response response = await _restClient.post(
        OrderConfirmPathApi.transConfirmOrders(subAccount: subAccount),
        data: param?.toJson(),
        options: Options(headers: UtilitiesUtils.orderHeaders),
      );

      final appBaseResponse = AppBaseResponse.fromJson(response.data);

      if (appBaseResponse.isSuccess()) {
        return appBaseResponse;
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<AppBaseResponse> transConfirmAllOrders({
    String? subAccount,
    OrderConfirmRequestObj? param,
  }) async {
    try {
      Response response = await _restClient.post(
        OrderConfirmPathApi.transConfirmAllOrders(subAccount: subAccount),
        data: param?.toJson(),
        options: Options(headers: UtilitiesUtils.orderHeaders),
      );

      final appBaseResponse = AppBaseResponse.fromJson(response.data);

      if (appBaseResponse.isSuccess()) {
        return appBaseResponse;
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<Response> generalContractOTP() async {
    try {
      Response response = await _restClient.post(
        OrderConfirmPathApi.generalContractOTP,
        options: Options(headers: UtilitiesUtils.orderHeaders),
      );

      final appBaseResponse = BaseResponse.fromJson(
        response.data,
        (json) => json,
      );

      if (appBaseResponse.isSuccess) {
        return response;
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<Response> verifyContractOTP(String otp) async {
    try {
      Response response = await _restClient.post(
        OrderConfirmPathApi.verifyContractOTP,
        options: Options(headers: UtilitiesUtils.orderHeaders),
        data: {'otp': otp},
      );

      final appBaseResponse = BaseResponse.fromJson(
        response.data,
        (json) => json,
      );

      if (appBaseResponse.isSuccess) {
        return response;
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<Response> verifyTC13() async {
    try {
      Response response = await _restClient.put(
        OrderConfirmPathApi.verifyTC13,
        queryParameters: {'channel': 'APP'},
      );

      return response;
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }
}
