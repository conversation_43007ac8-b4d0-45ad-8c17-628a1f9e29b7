import 'package:tuple/tuple.dart';
import 'package:vp_common/widget/log_api/models/app_base_response.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_utility/model/response/check_model.dart';
import 'package:vp_utility/model/response/order_confirm/models/order_confirm_obj.dart';
import 'package:vp_utility/model/response/order_confirm/models/order_confirm_request_obj.dart';

abstract class OrderConfirmRepository {
  Future<Tuple3<List<OrderConfirmObj>, int, int>> confirmOrders({
    String? subAccount,
    String? fromDate,
    String? toDate,
    String? execType,
    String? symbol,
    required int pageIndex,
    required int pageSize,
  });

  Future<int> confirmOrdersInfor({String? subAccount});

  Future<CheckModel> checkConfirmOrders({String? subAccount, String? orderID});

  Future<CheckModel> checkConfirmAllOrders({String? subAccount});

  Future<AppBaseResponse> transConfirmOrders({
    String? subAccount,
    OrderConfirmRequestObj param,
  });

  Future<AppBaseResponse> transConfirmAllOrders({
    String? subAccount,
    OrderConfirmRequestObj param,
  });

  Future<Response> generalContractOTP();

  Future<Response> verifyContractOTP(String otp);

  Future<Response> verifyTC13();
}
