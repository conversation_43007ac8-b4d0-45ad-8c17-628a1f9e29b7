class OrderConfirmPathApi {
  // Path api lay danh sach xac nhan lenh
  static String confirmOrders({
    String? subAccount,
    String? fromDate,
    String? toDate,
    String? execType,
    String? symbol,
    required int pageIndex,
    required int pageSize,
  }) =>
      '/flex/report/accounts/$subAccount/confirmOrders?fromDate=$fromDate&toDate=$toDate&exectype=$execType&symbol=$symbol&pageNo=$pageIndex&pageSize=$pageSize';

  static String confirmOrdersInfor({String? subAccount}) {
    return '/flex/inq/accounts/$subAccount/confirmOrdersInfor';
  }

  // Path api kiem tra xac nhan lenh
  static String checkConfirmOrders({String? subAccount, String? orderId}) {
    return '/flex/tran/accounts/$subAccount/checkConfirmOrders';
  }

  static String checkConfirmAllOrders({String? subAccount}) {
    return '/flex/tran/accounts/$subAccount/checkConfirmAllOrders';
  }

  // Path api xac nhan lenh
  static String transConfirmOrders({String? subAccount}) {
    return '/flex/tran/accounts/$subAccount/confirmOrders';
  }

  static String transConfirmAllOrders({String? subAccount}) {
    return '/flex/tran/accounts/$subAccount/confirmAllOrders';
  }

  // Path api xac nhan lenh
  static String generalContractOTP = '/iam/external/sign-contract/generation';

  static String verifyContractOTP = '/iam/external/sign-contract/verification';
  static String verifyTC13 = '/iam/external/tc13/online-signing';
}
