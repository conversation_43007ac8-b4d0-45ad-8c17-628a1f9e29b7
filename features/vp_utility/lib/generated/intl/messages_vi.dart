// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "oc_all": MessageLookupByLibrary.simpleMessage("Tất cả"),
    "oc_apply": MessageLookupByLibrary.simpleMessage("Áp dụng"),
    "oc_buy": MessageLookupByLibrary.simpleMessage("Mua"),
    "oc_buy_order": MessageLookupByLibrary.simpleMessage("Lệnh mua"),
    "oc_close": MessageLookupByLibrary.simpleMessage("Đóng"),
    "oc_command_type": MessageLookupByLibrary.simpleMessage("Loại lệnh"),
    "oc_custom": MessageLookupByLibrary.simpleMessage("Tùy chọn"),
    "oc_desc_confirm_order": MessageLookupByLibrary.simpleMessage(
      "Bạn có muốn xác nhận các lệnh đã chọn?",
    ),
    "oc_joint_volume": MessageLookupByLibrary.simpleMessage("Khối lượng khớp"),
    "oc_margin": MessageLookupByLibrary.simpleMessage("Ký quỹ"),
    "oc_month": MessageLookupByLibrary.simpleMessage("tháng"),
    "oc_no_data": MessageLookupByLibrary.simpleMessage(
      "Hiện tại không có dữ liệu",
    ),
    "oc_normal": MessageLookupByLibrary.simpleMessage("Thường"),
    "oc_oder_confirm_success": MessageLookupByLibrary.simpleMessage(
      "Xác nhận lệnh thành công",
    ),
    "oc_order_confirm_all": MessageLookupByLibrary.simpleMessage(
      "Xác nhận tất cả lệnh",
    ),
    "oc_order_no_select": MessageLookupByLibrary.simpleMessage(
      "Bạn vui lòng chọn lệnh cần xác nhận",
    ),
    "oc_order_time": MessageLookupByLibrary.simpleMessage("Thời gian đặt lệnh"),
    "oc_reset": MessageLookupByLibrary.simpleMessage("Đặt lại"),
    "oc_sell": MessageLookupByLibrary.simpleMessage("Bán"),
    "oc_sell_order": MessageLookupByLibrary.simpleMessage("Lệnh bán"),
    "oc_status": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "oc_sub_account": MessageLookupByLibrary.simpleMessage("Tiểu khoản"),
    "oc_time": MessageLookupByLibrary.simpleMessage("Thời gian"),
    "oc_total_money": MessageLookupByLibrary.simpleMessage("Tổng tiền"),
    "rc_EXPIRED": MessageLookupByLibrary.simpleMessage("Hiệu lực đến"),
    "rc_active": MessageLookupByLibrary.simpleMessage("Hoạt động"),
    "rc_basic": MessageLookupByLibrary.simpleMessage("Cơ bản"),
    "rc_bigger": MessageLookupByLibrary.simpleMessage("Lớn hơn"),
    "rc_buy": MessageLookupByLibrary.simpleMessage("Mua"),
    "rc_collapse": MessageLookupByLibrary.simpleMessage("Thu gọn"),
    "rc_consultingExpert": MessageLookupByLibrary.simpleMessage(
      "Chuyên gia tư vấn",
    ),
    "rc_d": MessageLookupByLibrary.simpleMessage("10 ngày (T+)"),
    "rc_exmo": MessageLookupByLibrary.simpleMessage("Expert momentum"),
    "rc_expire": MessageLookupByLibrary.simpleMessage("Hiệu lực đến"),
    "rc_expiryDate": MessageLookupByLibrary.simpleMessage("Ngày hết hiệu lực"),
    "rc_generalRc": MessageLookupByLibrary.simpleMessage("Khuyến nghị chung"),
    "rc_haveStock": MessageLookupByLibrary.simpleMessage(
      "Bạn không có cổ phiếu",
    ),
    "rc_hold": MessageLookupByLibrary.simpleMessage("Nắm giữ"),
    "rc_holding": MessageLookupByLibrary.simpleMessage("Nắm giữ"),
    "rc_invRecommendation": MessageLookupByLibrary.simpleMessage(
      "Khuyến nghị đầu tư",
    ),
    "rc_investmentThesis": MessageLookupByLibrary.simpleMessage(
      "Luận điểm đầu tư",
    ),
    "rc_investmentTime": MessageLookupByLibrary.simpleMessage(
      "Thời gian đầu tư",
    ),
    "rc_less": MessageLookupByLibrary.simpleMessage("Nhỏ hơn"),
    "rc_m": MessageLookupByLibrary.simpleMessage("1 tháng"),
    "rc_method": MessageLookupByLibrary.simpleMessage("Phương pháp"),
    "rc_noFilterData": MessageLookupByLibrary.simpleMessage(
      "Không có khuyến nghị nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại",
    ),
    "rc_no_data": MessageLookupByLibrary.simpleMessage(
      "Hiện tại không có dữ liệu",
    ),
    "rc_o1mu3m": MessageLookupByLibrary.simpleMessage("Trên 1 tháng - 3 tháng"),
    "rc_o1y": MessageLookupByLibrary.simpleMessage("Trên 1 năm"),
    "rc_o3mu6m": MessageLookupByLibrary.simpleMessage("Trên 3 tháng - 6 tháng"),
    "rc_o6mu1y": MessageLookupByLibrary.simpleMessage("Trên 6 tháng - 1 năm"),
    "rc_operatingEfficiency": MessageLookupByLibrary.simpleMessage(
      "Hiệu quả hoạt động",
    ),
    "rc_placeBuyOrder": MessageLookupByLibrary.simpleMessage("Mua"),
    "rc_placeSellOrder": MessageLookupByLibrary.simpleMessage("Bán"),
    "rc_purchasingAbility": MessageLookupByLibrary.simpleMessage("Sức mua"),
    "rc_reason": MessageLookupByLibrary.simpleMessage("Lý do"),
    "rc_recommendedDate": MessageLookupByLibrary.simpleMessage(
      "Ngày khuyến nghị",
    ),
    "rc_recommendedPerson": MessageLookupByLibrary.simpleMessage(
      "Người khuyến nghị",
    ),
    "rc_recommendedTime": MessageLookupByLibrary.simpleMessage(
      "Thời gian khuyến nghị",
    ),
    "rc_secmo": MessageLookupByLibrary.simpleMessage("Sector momentum"),
    "rc_seeMore": MessageLookupByLibrary.simpleMessage("Xem thêm"),
    "rc_sell": MessageLookupByLibrary.simpleMessage("Bán"),
    "rc_smcmo": MessageLookupByLibrary.simpleMessage("Small cap momentum"),
    "rc_status_expired": MessageLookupByLibrary.simpleMessage("Hết hiệu lực"),
    "rc_stock": MessageLookupByLibrary.simpleMessage("Cổ phiếu"),
    "rc_stockCode": MessageLookupByLibrary.simpleMessage("Mã chứng khoán"),
    "rc_stockRc": MessageLookupByLibrary.simpleMessage("Khuyến nghị CP"),
    "rc_stopLoss": MessageLookupByLibrary.simpleMessage("Cắt lỗ"),
    "rc_stopLossPrice": MessageLookupByLibrary.simpleMessage("Giá cắt lỗ"),
    "rc_suggestedPrice": MessageLookupByLibrary.simpleMessage("Giá gợi ý"),
    "rc_suggestedVolume": MessageLookupByLibrary.simpleMessage(
      "Khối lượng gợi ý",
    ),
    "rc_takeProfit": MessageLookupByLibrary.simpleMessage("Chốt lời"),
    "rc_targetPrice": MessageLookupByLibrary.simpleMessage("Giá mục tiêu"),
    "rc_targetPriceVsCurrent": MessageLookupByLibrary.simpleMessage(
      "% Giá mục tiêu so với hiện tại",
    ),
    "rc_technique": MessageLookupByLibrary.simpleMessage("Kỹ thuật"),
    "rc_typeRecommendation": MessageLookupByLibrary.simpleMessage(
      "Loại khuyến nghị",
    ),
    "rc_validTill": MessageLookupByLibrary.simpleMessage("Hiệu lực đến"),
    "rc_valmo": MessageLookupByLibrary.simpleMessage("Value momentum"),
    "rc_w": MessageLookupByLibrary.simpleMessage("1 tuần"),
    "utility_advance_payment": MessageLookupByLibrary.simpleMessage(
      "Ứng trước tiền bán",
    ),
    "utility_eMonie": MessageLookupByLibrary.simpleMessage("Tính năng eMonie"),
    "utility_event_calendar": MessageLookupByLibrary.simpleMessage(
      "Lịch sự kiện",
    ),
    "utility_filter_description": MessageLookupByLibrary.simpleMessage(
      "Tạo bộ lọc theo tiêu chí, nâng cao khả năng ra quyết định đầu tư",
    ),
    "utility_financial_service_package": MessageLookupByLibrary.simpleMessage(
      "Dịch vụ tài chính",
    ),
    "utility_matchedSellingValue": MessageLookupByLibrary.simpleMessage(
      "Giá trị khớp bán",
    ),
    "utility_model_portfolio": MessageLookupByLibrary.simpleMessage(
      "Danh mục đầu tư mẫu",
    ),
    "utility_order_confirm": MessageLookupByLibrary.simpleMessage(
      "Xác nhận lệnh",
    ),
    "utility_priceMatchesAverage": MessageLookupByLibrary.simpleMessage(
      "Giá khớp TB",
    ),
    "utility_profitAndLoss": MessageLookupByLibrary.simpleMessage("Lãi/lỗ"),
    "utility_purchaseFee": MessageLookupByLibrary.simpleMessage("Phí mua"),
    "utility_purchaseMatchValue": MessageLookupByLibrary.simpleMessage(
      "Giá trị khớp mua",
    ),
    "utility_purchaseVolume": MessageLookupByLibrary.simpleMessage(
      "Khối lượng mua",
    ),
    "utility_ranking_industry_stock_ranking":
        MessageLookupByLibrary.simpleMessage("Xếp hạng Ngành/CP"),
    "utility_recommendation_invRecommendation":
        MessageLookupByLibrary.simpleMessage("Khuyến nghị đầu tư"),
    "utility_register_buy": MessageLookupByLibrary.simpleMessage(
      "CK chứng khoán",
    ),
    "utility_saleprice": MessageLookupByLibrary.simpleMessage("Giá bán"),
    "utility_salesTax": MessageLookupByLibrary.simpleMessage("Thuế bán"),
    "utility_salesVolume": MessageLookupByLibrary.simpleMessage(
      "Khối lượng bán",
    ),
    "utility_sellingFee": MessageLookupByLibrary.simpleMessage("Phí bán"),
    "utility_stock": MessageLookupByLibrary.simpleMessage("Cổ phiếu"),
    "utility_stockFilter": MessageLookupByLibrary.simpleMessage(
      "Bộ lọc cổ phiếu",
    ),
    "utility_symbol": MessageLookupByLibrary.simpleMessage("Mã"),
    "utility_util_market_analytic": MessageLookupByLibrary.simpleMessage(
      "Thông tin thị trường",
    ),
    "utility_util_stock_transfer": MessageLookupByLibrary.simpleMessage(
      "CK chứng khoán",
    ),
    "utility_utilities": MessageLookupByLibrary.simpleMessage("Tiện ích"),
    "utility_utils_stock_alert": MessageLookupByLibrary.simpleMessage(
      "Cảnh báo cổ phiếu",
    ),
    "utility_valueOfCapitalSold": MessageLookupByLibrary.simpleMessage(
      "Giá trị vốn đã bán",
    ),
  };
}
