// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "oc_all": MessageLookupByLibrary.simpleMessage("All"),
    "oc_apply": MessageLookupByLibrary.simpleMessage("Apply"),
    "oc_buy": MessageLookupByLibrary.simpleMessage("Buy"),
    "oc_buy_order": MessageLookupByLibrary.simpleMessage("Buy order"),
    "oc_close": MessageLookupByLibrary.simpleMessage("Close"),
    "oc_command_type": MessageLookupByLibrary.simpleMessage("Command type"),
    "oc_custom": MessageLookupByLibrary.simpleMessage("Custom"),
    "oc_desc_confirm_order": MessageLookupByLibrary.simpleMessage(
      "Do you have confirmation that you want to confirm the selected order?",
    ),
    "oc_joint_volume": MessageLookupByLibrary.simpleMessage("Joint volume"),
    "oc_margin": MessageLookupByLibrary.simpleMessage("Margin"),
    "oc_month": MessageLookupByLibrary.simpleMessage("month"),
    "oc_no_data": MessageLookupByLibrary.simpleMessage("Currently no data"),
    "oc_normal": MessageLookupByLibrary.simpleMessage("Normal"),
    "oc_oder_confirm_success": MessageLookupByLibrary.simpleMessage(
      "Command Confirmation Successful",
    ),
    "oc_order_confirm_all": MessageLookupByLibrary.simpleMessage(
      "Confirm all orders",
    ),
    "oc_order_no_select": MessageLookupByLibrary.simpleMessage(
      "Please select the order you want to confirm",
    ),
    "oc_order_time": MessageLookupByLibrary.simpleMessage("Order time"),
    "oc_reset": MessageLookupByLibrary.simpleMessage("Reset"),
    "oc_sell": MessageLookupByLibrary.simpleMessage("Sell"),
    "oc_sell_order": MessageLookupByLibrary.simpleMessage("Sell order"),
    "oc_status": MessageLookupByLibrary.simpleMessage("Status"),
    "oc_sub_account": MessageLookupByLibrary.simpleMessage("Sub-account"),
    "oc_time": MessageLookupByLibrary.simpleMessage("Time"),
    "oc_total_money": MessageLookupByLibrary.simpleMessage("Total money"),
    "rc_EXPIRED": MessageLookupByLibrary.simpleMessage("Expired"),
    "rc_active": MessageLookupByLibrary.simpleMessage("Active"),
    "rc_basic": MessageLookupByLibrary.simpleMessage("Basic"),
    "rc_bigger": MessageLookupByLibrary.simpleMessage("Bigger"),
    "rc_buy": MessageLookupByLibrary.simpleMessage("Buy"),
    "rc_collapse": MessageLookupByLibrary.simpleMessage("Collapse"),
    "rc_consultingExpert": MessageLookupByLibrary.simpleMessage(
      "Consulting Expert",
    ),
    "rc_d": MessageLookupByLibrary.simpleMessage("10 days (T+)"),
    "rc_exmo": MessageLookupByLibrary.simpleMessage("Expert momentum"),
    "rc_expire": MessageLookupByLibrary.simpleMessage("Expired"),
    "rc_expiryDate": MessageLookupByLibrary.simpleMessage("Expiry Date"),
    "rc_generalRc": MessageLookupByLibrary.simpleMessage(
      "General Recommendation",
    ),
    "rc_haveStock": MessageLookupByLibrary.simpleMessage(
      "You don\'t have this stock",
    ),
    "rc_hold": MessageLookupByLibrary.simpleMessage("Hold"),
    "rc_holding": MessageLookupByLibrary.simpleMessage("Holding"),
    "rc_invRecommendation": MessageLookupByLibrary.simpleMessage(
      "Investment Recommendation",
    ),
    "rc_investmentThesis": MessageLookupByLibrary.simpleMessage(
      "Investment thesis",
    ),
    "rc_investmentTime": MessageLookupByLibrary.simpleMessage(
      "Investment Time",
    ),
    "rc_less": MessageLookupByLibrary.simpleMessage("Less"),
    "rc_m": MessageLookupByLibrary.simpleMessage("1 month"),
    "rc_method": MessageLookupByLibrary.simpleMessage("Method"),
    "rc_noFilterData": MessageLookupByLibrary.simpleMessage(
      "No recommendations match the filter. Please change the filter conditions and try again.",
    ),
    "rc_no_data": MessageLookupByLibrary.simpleMessage(
      "No data available at this time",
    ),
    "rc_o1mu3m": MessageLookupByLibrary.simpleMessage(
      "Over 1 month - 3 months",
    ),
    "rc_o1y": MessageLookupByLibrary.simpleMessage("Over 1 year"),
    "rc_o3mu6m": MessageLookupByLibrary.simpleMessage(
      "Over 3 months - 6 months",
    ),
    "rc_o6mu1y": MessageLookupByLibrary.simpleMessage("Over 6 months - 1 year"),
    "rc_operatingEfficiency": MessageLookupByLibrary.simpleMessage(
      "Operating Efficiency",
    ),
    "rc_placeBuyOrder": MessageLookupByLibrary.simpleMessage("Buy"),
    "rc_placeSellOrder": MessageLookupByLibrary.simpleMessage("Sell"),
    "rc_purchasingAbility": MessageLookupByLibrary.simpleMessage(
      "Purchasing Ability",
    ),
    "rc_reason": MessageLookupByLibrary.simpleMessage("Reason"),
    "rc_recommendedDate": MessageLookupByLibrary.simpleMessage(
      "Recommendation Date",
    ),
    "rc_recommendedPerson": MessageLookupByLibrary.simpleMessage(
      "Recommended By",
    ),
    "rc_recommendedTime": MessageLookupByLibrary.simpleMessage(
      "Recommendation Time",
    ),
    "rc_secmo": MessageLookupByLibrary.simpleMessage("Sector momentum"),
    "rc_seeMore": MessageLookupByLibrary.simpleMessage("See More"),
    "rc_sell": MessageLookupByLibrary.simpleMessage("Sell"),
    "rc_smcmo": MessageLookupByLibrary.simpleMessage("Small cap momentum"),
    "rc_status_expired": MessageLookupByLibrary.simpleMessage("Expired"),
    "rc_stock": MessageLookupByLibrary.simpleMessage("Stock"),
    "rc_stockCode": MessageLookupByLibrary.simpleMessage("Stock Code"),
    "rc_stockRc": MessageLookupByLibrary.simpleMessage("Stock Recommendation"),
    "rc_stopLoss": MessageLookupByLibrary.simpleMessage("Stop Loss"),
    "rc_stopLossPrice": MessageLookupByLibrary.simpleMessage("Stop Loss Price"),
    "rc_suggestedPrice": MessageLookupByLibrary.simpleMessage(
      "Suggested Price",
    ),
    "rc_suggestedVolume": MessageLookupByLibrary.simpleMessage(
      "Suggested Volume",
    ),
    "rc_takeProfit": MessageLookupByLibrary.simpleMessage("Take Profit"),
    "rc_targetPrice": MessageLookupByLibrary.simpleMessage("Target Price"),
    "rc_targetPriceVsCurrent": MessageLookupByLibrary.simpleMessage(
      "% Target Price vs Current",
    ),
    "rc_technique": MessageLookupByLibrary.simpleMessage("Technical"),
    "rc_typeRecommendation": MessageLookupByLibrary.simpleMessage(
      "Recommendation Type",
    ),
    "rc_validTill": MessageLookupByLibrary.simpleMessage("Valid Till"),
    "rc_valmo": MessageLookupByLibrary.simpleMessage("Value momentum"),
    "rc_w": MessageLookupByLibrary.simpleMessage("1 week"),
    "utility_advance_payment": MessageLookupByLibrary.simpleMessage(
      "Advance payment",
    ),
    "utility_eMonie": MessageLookupByLibrary.simpleMessage("eMonie"),
    "utility_event_calendar": MessageLookupByLibrary.simpleMessage(
      "Calendar event",
    ),
    "utility_filter_description": MessageLookupByLibrary.simpleMessage(
      "Create filters by criteria, improve investment decision making",
    ),
    "utility_financial_service_package": MessageLookupByLibrary.simpleMessage(
      "Financial services",
    ),
    "utility_matchedSellingValue": MessageLookupByLibrary.simpleMessage(
      "Giá trị khớp bán",
    ),
    "utility_model_portfolio": MessageLookupByLibrary.simpleMessage(
      "Model portfolio",
    ),
    "utility_order_confirm": MessageLookupByLibrary.simpleMessage(
      "Order confirmation",
    ),
    "utility_priceMatchesAverage": MessageLookupByLibrary.simpleMessage(
      "Giá khớp TB",
    ),
    "utility_profitAndLoss": MessageLookupByLibrary.simpleMessage(
      "Profit/loss",
    ),
    "utility_purchaseFee": MessageLookupByLibrary.simpleMessage("Phí mua"),
    "utility_purchaseMatchValue": MessageLookupByLibrary.simpleMessage(
      "Giá trị khớp mua",
    ),
    "utility_purchaseVolume": MessageLookupByLibrary.simpleMessage(
      "Khối lượng mua",
    ),
    "utility_ranking_industry_stock_ranking":
        MessageLookupByLibrary.simpleMessage("Ranking industry stock ranking"),
    "utility_recommendation_invRecommendation":
        MessageLookupByLibrary.simpleMessage(
          "Recommendation invRecommendation",
        ),
    "utility_register_buy": MessageLookupByLibrary.simpleMessage(
      "Register the right to buy",
    ),
    "utility_saleprice": MessageLookupByLibrary.simpleMessage("Giá bán"),
    "utility_salesTax": MessageLookupByLibrary.simpleMessage("Thuế bán"),
    "utility_salesVolume": MessageLookupByLibrary.simpleMessage(
      "Khối lượng bán",
    ),
    "utility_sellingFee": MessageLookupByLibrary.simpleMessage("Phí bán"),
    "utility_stock": MessageLookupByLibrary.simpleMessage("Stock"),
    "utility_stockFilter": MessageLookupByLibrary.simpleMessage("Stock filter"),
    "utility_symbol": MessageLookupByLibrary.simpleMessage("Symbol"),
    "utility_util_market_analytic": MessageLookupByLibrary.simpleMessage(
      "Market analysis",
    ),
    "utility_util_stock_transfer": MessageLookupByLibrary.simpleMessage(
      "Securities transfer",
    ),
    "utility_utilities": MessageLookupByLibrary.simpleMessage("Utilities"),
    "utility_utils_stock_alert": MessageLookupByLibrary.simpleMessage(
      "Stock alert",
    ),
    "utility_valueOfCapitalSold": MessageLookupByLibrary.simpleMessage(
      "Giá trị vốn đã bán",
    ),
  };
}
