/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/checkedbox.svg
  SvgGenImage get checkedbox =>
      const SvgGenImage('assets/icons/checkedbox.svg');

  /// File path: assets/icons/clear_text.svg
  SvgGenImage get clearText => const SvgGenImage('assets/icons/clear_text.svg');

  /// File path: assets/icons/clipboardalt.svg
  SvgGenImage get clipboardalt =>
      const SvgGenImage('assets/icons/clipboardalt.svg');

  /// File path: assets/icons/edit.svg
  SvgGenImage get edit => const SvgGenImage('assets/icons/edit.svg');

  /// File path: assets/icons/empty_layout.svg
  SvgGenImage get emptyLayout =>
      const SvgGenImage('assets/icons/empty_layout.svg');

  /// File path: assets/icons/filter_stock.svg
  SvgGenImage get filterStock =>
      const SvgGenImage('assets/icons/filter_stock.svg');

  /// File path: assets/icons/handle.svg
  SvgGenImage get handle => const SvgGenImage('assets/icons/handle.svg');

  /// File path: assets/icons/ic_ai.svg
  SvgGenImage get icAi => const SvgGenImage('assets/icons/ic_ai.svg');

  /// File path: assets/icons/ic_asset.svg
  SvgGenImage get icAsset => const SvgGenImage('assets/icons/ic_asset.svg');

  /// File path: assets/icons/ic_board_checked.svg
  SvgGenImage get icBoardChecked =>
      const SvgGenImage('assets/icons/ic_board_checked.svg');

  /// File path: assets/icons/ic_calendar.svg
  SvgGenImage get icCalendar =>
      const SvgGenImage('assets/icons/ic_calendar.svg');

  /// File path: assets/icons/ic_checkbox_continue_rect.svg
  SvgGenImage get icCheckboxContinueRect =>
      const SvgGenImage('assets/icons/ic_checkbox_continue_rect.svg');

  /// File path: assets/icons/ic_checkbox_none_rect.svg
  SvgGenImage get icCheckboxNoneRect =>
      const SvgGenImage('assets/icons/ic_checkbox_none_rect.svg');

  /// File path: assets/icons/ic_checkbox_rect.svg
  SvgGenImage get icCheckboxRect =>
      const SvgGenImage('assets/icons/ic_checkbox_rect.svg');

  /// File path: assets/icons/ic_clear_text.svg
  SvgGenImage get icClearText =>
      const SvgGenImage('assets/icons/ic_clear_text.svg');

  /// File path: assets/icons/ic_date_new.svg
  SvgGenImage get icDateNew =>
      const SvgGenImage('assets/icons/ic_date_new.svg');

  /// File path: assets/icons/ic_date_picker.svg
  SvgGenImage get icDatePicker =>
      const SvgGenImage('assets/icons/ic_date_picker.svg');

  /// File path: assets/icons/ic_event.svg
  SvgGenImage get icEvent => const SvgGenImage('assets/icons/ic_event.svg');

  /// File path: assets/icons/ic_file_user.svg
  SvgGenImage get icFileUser =>
      const SvgGenImage('assets/icons/ic_file_user.svg');

  /// File path: assets/icons/ic_filter.svg
  SvgGenImage get icFilter => const SvgGenImage('assets/icons/ic_filter.svg');

  /// File path: assets/icons/ic_have_filter.svg
  SvgGenImage get icHaveFilter =>
      const SvgGenImage('assets/icons/ic_have_filter.svg');

  /// File path: assets/icons/ic_money_bag.svg
  SvgGenImage get icMoneyBag =>
      const SvgGenImage('assets/icons/ic_money_bag.svg');

  /// File path: assets/icons/ic_ranking.svg
  SvgGenImage get icRanking => const SvgGenImage('assets/icons/ic_ranking.svg');

  /// File path: assets/icons/ic_search_new.svg
  SvgGenImage get icSearchNew =>
      const SvgGenImage('assets/icons/ic_search_new.svg');

  /// File path: assets/icons/ic_sec.svg
  SvgGenImage get icSec => const SvgGenImage('assets/icons/ic_sec.svg');

  /// File path: assets/icons/ic_stock_aleart.svg
  SvgGenImage get icStockAleart =>
      const SvgGenImage('assets/icons/ic_stock_aleart.svg');

  /// File path: assets/icons/ic_transfer_money.svg
  SvgGenImage get icTransferMoney =>
      const SvgGenImage('assets/icons/ic_transfer_money.svg');

  /// File path: assets/icons/money.svg
  SvgGenImage get money => const SvgGenImage('assets/icons/money.svg');

  /// File path: assets/icons/search.svg
  SvgGenImage get search => const SvgGenImage('assets/icons/search.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    checkedbox,
    clearText,
    clipboardalt,
    edit,
    emptyLayout,
    filterStock,
    handle,
    icAi,
    icAsset,
    icBoardChecked,
    icCalendar,
    icCheckboxContinueRect,
    icCheckboxNoneRect,
    icCheckboxRect,
    icClearText,
    icDateNew,
    icDatePicker,
    icEvent,
    icFileUser,
    icFilter,
    icHaveFilter,
    icMoneyBag,
    icRanking,
    icSearchNew,
    icSec,
    icStockAleart,
    icTransferMoney,
    money,
    search,
  ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/error.png
  AssetGenImage get error => const AssetGenImage('assets/images/error.png');

  /// List of all assets
  List<AssetGenImage> get values => [error];
}

class VpUtilityAssets {
  const VpUtilityAssets._();

  static const String package = 'vp_utility';

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  static const String package = 'vp_utility';

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
  }) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_utility/$_assetName';
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  static const String package = 'vp_utility';

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_utility/$_assetName';
}
