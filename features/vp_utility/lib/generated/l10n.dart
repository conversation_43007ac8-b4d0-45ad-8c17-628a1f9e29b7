// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class VpUtilityLocalize {
  VpUtilityLocalize();

  static VpUtilityLocalize? _current;

  static VpUtilityLocalize get current {
    assert(
      _current != null,
      'No instance of VpUtilityLocalize was loaded. Try to initialize the VpUtilityLocalize delegate before accessing VpUtilityLocalize.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<VpUtilityLocalize> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = VpUtilityLocalize();
      VpUtilityLocalize._current = instance;

      return instance;
    });
  }

  static VpUtilityLocalize of(BuildContext context) {
    final instance = VpUtilityLocalize.maybeOf(context);
    assert(
      instance != null,
      'No instance of VpUtilityLocalize present in the widget tree. Did you add VpUtilityLocalize.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static VpUtilityLocalize? maybeOf(BuildContext context) {
    return Localizations.of<VpUtilityLocalize>(context, VpUtilityLocalize);
  }

  /// `Investment Recommendation`
  String get rc_invRecommendation {
    return Intl.message(
      'Investment Recommendation',
      name: 'rc_invRecommendation',
      desc: '',
      args: [],
    );
  }

  /// `Consulting Expert`
  String get rc_consultingExpert {
    return Intl.message(
      'Consulting Expert',
      name: 'rc_consultingExpert',
      desc: '',
      args: [],
    );
  }

  /// `Expired`
  String get rc_expire {
    return Intl.message('Expired', name: 'rc_expire', desc: '', args: []);
  }

  /// `Holding`
  String get rc_holding {
    return Intl.message('Holding', name: 'rc_holding', desc: '', args: []);
  }

  /// `Active`
  String get rc_active {
    return Intl.message('Active', name: 'rc_active', desc: '', args: []);
  }

  /// `Recommendation Time`
  String get rc_recommendedTime {
    return Intl.message(
      'Recommendation Time',
      name: 'rc_recommendedTime',
      desc: '',
      args: [],
    );
  }

  /// `Recommendation Type`
  String get rc_typeRecommendation {
    return Intl.message(
      'Recommendation Type',
      name: 'rc_typeRecommendation',
      desc: '',
      args: [],
    );
  }

  /// `% Target Price vs Current`
  String get rc_targetPriceVsCurrent {
    return Intl.message(
      '% Target Price vs Current',
      name: 'rc_targetPriceVsCurrent',
      desc: '',
      args: [],
    );
  }

  /// `Operating Efficiency`
  String get rc_operatingEfficiency {
    return Intl.message(
      'Operating Efficiency',
      name: 'rc_operatingEfficiency',
      desc: '',
      args: [],
    );
  }

  /// `Method`
  String get rc_method {
    return Intl.message('Method', name: 'rc_method', desc: '', args: []);
  }

  /// `Target Price`
  String get rc_targetPrice {
    return Intl.message(
      'Target Price',
      name: 'rc_targetPrice',
      desc: '',
      args: [],
    );
  }

  /// `Stop Loss Price`
  String get rc_stopLossPrice {
    return Intl.message(
      'Stop Loss Price',
      name: 'rc_stopLossPrice',
      desc: '',
      args: [],
    );
  }

  /// `Recommendation Date`
  String get rc_recommendedDate {
    return Intl.message(
      'Recommendation Date',
      name: 'rc_recommendedDate',
      desc: '',
      args: [],
    );
  }

  /// `Expiry Date`
  String get rc_expiryDate {
    return Intl.message(
      'Expiry Date',
      name: 'rc_expiryDate',
      desc: '',
      args: [],
    );
  }

  /// `Investment Time`
  String get rc_investmentTime {
    return Intl.message(
      'Investment Time',
      name: 'rc_investmentTime',
      desc: '',
      args: [],
    );
  }

  /// `Reason`
  String get rc_reason {
    return Intl.message('Reason', name: 'rc_reason', desc: '', args: []);
  }

  /// `Buy`
  String get rc_placeBuyOrder {
    return Intl.message('Buy', name: 'rc_placeBuyOrder', desc: '', args: []);
  }

  /// `Sell`
  String get rc_placeSellOrder {
    return Intl.message('Sell', name: 'rc_placeSellOrder', desc: '', args: []);
  }

  /// `No recommendations match the filter. Please change the filter conditions and try again.`
  String get rc_noFilterData {
    return Intl.message(
      'No recommendations match the filter. Please change the filter conditions and try again.',
      name: 'rc_noFilterData',
      desc: '',
      args: [],
    );
  }

  /// `Stock`
  String get rc_stock {
    return Intl.message('Stock', name: 'rc_stock', desc: '', args: []);
  }

  /// `1 week`
  String get rc_w {
    return Intl.message('1 week', name: 'rc_w', desc: '', args: []);
  }

  /// `10 days (T+)`
  String get rc_d {
    return Intl.message('10 days (T+)', name: 'rc_d', desc: '', args: []);
  }

  /// `1 month`
  String get rc_m {
    return Intl.message('1 month', name: 'rc_m', desc: '', args: []);
  }

  /// `Over 1 month - 3 months`
  String get rc_o1mu3m {
    return Intl.message(
      'Over 1 month - 3 months',
      name: 'rc_o1mu3m',
      desc: '',
      args: [],
    );
  }

  /// `Over 3 months - 6 months`
  String get rc_o3mu6m {
    return Intl.message(
      'Over 3 months - 6 months',
      name: 'rc_o3mu6m',
      desc: '',
      args: [],
    );
  }

  /// `Over 6 months - 1 year`
  String get rc_o6mu1y {
    return Intl.message(
      'Over 6 months - 1 year',
      name: 'rc_o6mu1y',
      desc: '',
      args: [],
    );
  }

  /// `Over 1 year`
  String get rc_o1y {
    return Intl.message('Over 1 year', name: 'rc_o1y', desc: '', args: []);
  }

  /// `Stock Code`
  String get rc_stockCode {
    return Intl.message('Stock Code', name: 'rc_stockCode', desc: '', args: []);
  }

  /// `Stock Recommendation`
  String get rc_stockRc {
    return Intl.message(
      'Stock Recommendation',
      name: 'rc_stockRc',
      desc: '',
      args: [],
    );
  }

  /// `General Recommendation`
  String get rc_generalRc {
    return Intl.message(
      'General Recommendation',
      name: 'rc_generalRc',
      desc: '',
      args: [],
    );
  }

  /// `Basic`
  String get rc_basic {
    return Intl.message('Basic', name: 'rc_basic', desc: '', args: []);
  }

  /// `Technical`
  String get rc_technique {
    return Intl.message('Technical', name: 'rc_technique', desc: '', args: []);
  }

  /// `Value momentum`
  String get rc_valmo {
    return Intl.message('Value momentum', name: 'rc_valmo', desc: '', args: []);
  }

  /// `Expert momentum`
  String get rc_exmo {
    return Intl.message('Expert momentum', name: 'rc_exmo', desc: '', args: []);
  }

  /// `Sector momentum`
  String get rc_secmo {
    return Intl.message(
      'Sector momentum',
      name: 'rc_secmo',
      desc: '',
      args: [],
    );
  }

  /// `Small cap momentum`
  String get rc_smcmo {
    return Intl.message(
      'Small cap momentum',
      name: 'rc_smcmo',
      desc: '',
      args: [],
    );
  }

  /// `Buy`
  String get rc_buy {
    return Intl.message('Buy', name: 'rc_buy', desc: '', args: []);
  }

  /// `Sell`
  String get rc_sell {
    return Intl.message('Sell', name: 'rc_sell', desc: '', args: []);
  }

  /// `Hold`
  String get rc_hold {
    return Intl.message('Hold', name: 'rc_hold', desc: '', args: []);
  }

  /// `Investment thesis`
  String get rc_investmentThesis {
    return Intl.message(
      'Investment thesis',
      name: 'rc_investmentThesis',
      desc: '',
      args: [],
    );
  }

  /// `Collapse`
  String get rc_collapse {
    return Intl.message('Collapse', name: 'rc_collapse', desc: '', args: []);
  }

  /// `See More`
  String get rc_seeMore {
    return Intl.message('See More', name: 'rc_seeMore', desc: '', args: []);
  }

  /// `Less`
  String get rc_less {
    return Intl.message('Less', name: 'rc_less', desc: '', args: []);
  }

  /// `Bigger`
  String get rc_bigger {
    return Intl.message('Bigger', name: 'rc_bigger', desc: '', args: []);
  }

  /// `Suggested Volume`
  String get rc_suggestedVolume {
    return Intl.message(
      'Suggested Volume',
      name: 'rc_suggestedVolume',
      desc: '',
      args: [],
    );
  }

  /// `Suggested Price`
  String get rc_suggestedPrice {
    return Intl.message(
      'Suggested Price',
      name: 'rc_suggestedPrice',
      desc: '',
      args: [],
    );
  }

  /// `Valid Till`
  String get rc_validTill {
    return Intl.message('Valid Till', name: 'rc_validTill', desc: '', args: []);
  }

  /// `Recommended By`
  String get rc_recommendedPerson {
    return Intl.message(
      'Recommended By',
      name: 'rc_recommendedPerson',
      desc: '',
      args: [],
    );
  }

  /// `Take Profit`
  String get rc_takeProfit {
    return Intl.message(
      'Take Profit',
      name: 'rc_takeProfit',
      desc: '',
      args: [],
    );
  }

  /// `Stop Loss`
  String get rc_stopLoss {
    return Intl.message('Stop Loss', name: 'rc_stopLoss', desc: '', args: []);
  }

  /// `Purchasing Ability`
  String get rc_purchasingAbility {
    return Intl.message(
      'Purchasing Ability',
      name: 'rc_purchasingAbility',
      desc: '',
      args: [],
    );
  }

  /// `You don't have this stock`
  String get rc_haveStock {
    return Intl.message(
      'You don\'t have this stock',
      name: 'rc_haveStock',
      desc: '',
      args: [],
    );
  }

  /// `Expired`
  String get rc_EXPIRED {
    return Intl.message('Expired', name: 'rc_EXPIRED', desc: '', args: []);
  }

  /// `No data available at this time`
  String get rc_no_data {
    return Intl.message(
      'No data available at this time',
      name: 'rc_no_data',
      desc: '',
      args: [],
    );
  }

  /// `Expired`
  String get rc_status_expired {
    return Intl.message(
      'Expired',
      name: 'rc_status_expired',
      desc: '',
      args: [],
    );
  }

  /// `Khối lượng mua`
  String get utility_purchaseVolume {
    return Intl.message(
      'Khối lượng mua',
      name: 'utility_purchaseVolume',
      desc: '',
      args: [],
    );
  }

  /// `Giá khớp TB`
  String get utility_priceMatchesAverage {
    return Intl.message(
      'Giá khớp TB',
      name: 'utility_priceMatchesAverage',
      desc: '',
      args: [],
    );
  }

  /// `Giá trị khớp mua`
  String get utility_purchaseMatchValue {
    return Intl.message(
      'Giá trị khớp mua',
      name: 'utility_purchaseMatchValue',
      desc: '',
      args: [],
    );
  }

  /// `Phí mua`
  String get utility_purchaseFee {
    return Intl.message(
      'Phí mua',
      name: 'utility_purchaseFee',
      desc: '',
      args: [],
    );
  }

  /// `Khối lượng bán`
  String get utility_salesVolume {
    return Intl.message(
      'Khối lượng bán',
      name: 'utility_salesVolume',
      desc: '',
      args: [],
    );
  }

  /// `Giá bán`
  String get utility_saleprice {
    return Intl.message(
      'Giá bán',
      name: 'utility_saleprice',
      desc: '',
      args: [],
    );
  }

  /// `Giá trị vốn đã bán`
  String get utility_valueOfCapitalSold {
    return Intl.message(
      'Giá trị vốn đã bán',
      name: 'utility_valueOfCapitalSold',
      desc: '',
      args: [],
    );
  }

  /// `Giá trị khớp bán`
  String get utility_matchedSellingValue {
    return Intl.message(
      'Giá trị khớp bán',
      name: 'utility_matchedSellingValue',
      desc: '',
      args: [],
    );
  }

  /// `Phí bán`
  String get utility_sellingFee {
    return Intl.message(
      'Phí bán',
      name: 'utility_sellingFee',
      desc: '',
      args: [],
    );
  }

  /// `Thuế bán`
  String get utility_salesTax {
    return Intl.message(
      'Thuế bán',
      name: 'utility_salesTax',
      desc: '',
      args: [],
    );
  }

  /// `Symbol`
  String get utility_symbol {
    return Intl.message('Symbol', name: 'utility_symbol', desc: '', args: []);
  }

  /// `Profit/loss`
  String get utility_profitAndLoss {
    return Intl.message(
      'Profit/loss',
      name: 'utility_profitAndLoss',
      desc: '',
      args: [],
    );
  }

  /// `Stock`
  String get utility_stock {
    return Intl.message('Stock', name: 'utility_stock', desc: '', args: []);
  }

  /// `Utilities`
  String get utility_utilities {
    return Intl.message(
      'Utilities',
      name: 'utility_utilities',
      desc: '',
      args: [],
    );
  }

  /// `Stock filter`
  String get utility_stockFilter {
    return Intl.message(
      'Stock filter',
      name: 'utility_stockFilter',
      desc: '',
      args: [],
    );
  }

  /// `Create filters by criteria, improve investment decision making`
  String get utility_filter_description {
    return Intl.message(
      'Create filters by criteria, improve investment decision making',
      name: 'utility_filter_description',
      desc: '',
      args: [],
    );
  }

  /// `Market analysis`
  String get utility_util_market_analytic {
    return Intl.message(
      'Market analysis',
      name: 'utility_util_market_analytic',
      desc: '',
      args: [],
    );
  }

  /// `Order confirmation`
  String get utility_order_confirm {
    return Intl.message(
      'Order confirmation',
      name: 'utility_order_confirm',
      desc: '',
      args: [],
    );
  }

  /// `Financial services`
  String get utility_financial_service_package {
    return Intl.message(
      'Financial services',
      name: 'utility_financial_service_package',
      desc: '',
      args: [],
    );
  }

  /// `Advance payment`
  String get utility_advance_payment {
    return Intl.message(
      'Advance payment',
      name: 'utility_advance_payment',
      desc: '',
      args: [],
    );
  }

  /// `Model portfolio`
  String get utility_model_portfolio {
    return Intl.message(
      'Model portfolio',
      name: 'utility_model_portfolio',
      desc: '',
      args: [],
    );
  }

  /// `Recommendation invRecommendation`
  String get utility_recommendation_invRecommendation {
    return Intl.message(
      'Recommendation invRecommendation',
      name: 'utility_recommendation_invRecommendation',
      desc: '',
      args: [],
    );
  }

  /// `Ranking industry stock ranking`
  String get utility_ranking_industry_stock_ranking {
    return Intl.message(
      'Ranking industry stock ranking',
      name: 'utility_ranking_industry_stock_ranking',
      desc: '',
      args: [],
    );
  }

  /// `Securities transfer`
  String get utility_util_stock_transfer {
    return Intl.message(
      'Securities transfer',
      name: 'utility_util_stock_transfer',
      desc: '',
      args: [],
    );
  }

  /// `Register the right to buy`
  String get utility_register_buy {
    return Intl.message(
      'Register the right to buy',
      name: 'utility_register_buy',
      desc: '',
      args: [],
    );
  }

  /// `Calendar event`
  String get utility_event_calendar {
    return Intl.message(
      'Calendar event',
      name: 'utility_event_calendar',
      desc: '',
      args: [],
    );
  }

  /// `Stock alert`
  String get utility_utils_stock_alert {
    return Intl.message(
      'Stock alert',
      name: 'utility_utils_stock_alert',
      desc: '',
      args: [],
    );
  }

  /// `eMonie`
  String get utility_eMonie {
    return Intl.message('eMonie', name: 'utility_eMonie', desc: '', args: []);
  }

  /// `Do you have confirmation that you want to confirm the selected order?`
  String get oc_desc_confirm_order {
    return Intl.message(
      'Do you have confirmation that you want to confirm the selected order?',
      name: 'oc_desc_confirm_order',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get oc_close {
    return Intl.message('Close', name: 'oc_close', desc: '', args: []);
  }

  /// `Please select the order you want to confirm`
  String get oc_order_no_select {
    return Intl.message(
      'Please select the order you want to confirm',
      name: 'oc_order_no_select',
      desc: '',
      args: [],
    );
  }

  /// `Total money`
  String get oc_total_money {
    return Intl.message(
      'Total money',
      name: 'oc_total_money',
      desc: '',
      args: [],
    );
  }

  /// `Joint volume`
  String get oc_joint_volume {
    return Intl.message(
      'Joint volume',
      name: 'oc_joint_volume',
      desc: '',
      args: [],
    );
  }

  /// `Order time`
  String get oc_order_time {
    return Intl.message(
      'Order time',
      name: 'oc_order_time',
      desc: '',
      args: [],
    );
  }

  /// `Command type`
  String get oc_command_type {
    return Intl.message(
      'Command type',
      name: 'oc_command_type',
      desc: '',
      args: [],
    );
  }

  /// `Sub-account`
  String get oc_sub_account {
    return Intl.message(
      'Sub-account',
      name: 'oc_sub_account',
      desc: '',
      args: [],
    );
  }

  /// `Time`
  String get oc_time {
    return Intl.message('Time', name: 'oc_time', desc: '', args: []);
  }

  /// `Buy`
  String get oc_buy {
    return Intl.message('Buy', name: 'oc_buy', desc: '', args: []);
  }

  /// `Sell`
  String get oc_sell {
    return Intl.message('Sell', name: 'oc_sell', desc: '', args: []);
  }

  /// `Normal`
  String get oc_normal {
    return Intl.message('Normal', name: 'oc_normal', desc: '', args: []);
  }

  /// `Margin`
  String get oc_margin {
    return Intl.message('Margin', name: 'oc_margin', desc: '', args: []);
  }

  /// `All`
  String get oc_all {
    return Intl.message('All', name: 'oc_all', desc: '', args: []);
  }

  /// `month`
  String get oc_month {
    return Intl.message('month', name: 'oc_month', desc: '', args: []);
  }

  /// `Custom`
  String get oc_custom {
    return Intl.message('Custom', name: 'oc_custom', desc: '', args: []);
  }

  /// `Buy order`
  String get oc_buy_order {
    return Intl.message('Buy order', name: 'oc_buy_order', desc: '', args: []);
  }

  /// `Sell order`
  String get oc_sell_order {
    return Intl.message(
      'Sell order',
      name: 'oc_sell_order',
      desc: '',
      args: [],
    );
  }

  /// `Apply`
  String get oc_apply {
    return Intl.message('Apply', name: 'oc_apply', desc: '', args: []);
  }

  /// `Reset`
  String get oc_reset {
    return Intl.message('Reset', name: 'oc_reset', desc: '', args: []);
  }

  /// `Command Confirmation Successful`
  String get oc_oder_confirm_success {
    return Intl.message(
      'Command Confirmation Successful',
      name: 'oc_oder_confirm_success',
      desc: '',
      args: [],
    );
  }

  /// `Currently no data`
  String get oc_no_data {
    return Intl.message(
      'Currently no data',
      name: 'oc_no_data',
      desc: '',
      args: [],
    );
  }

  /// `Confirm all orders`
  String get oc_order_confirm_all {
    return Intl.message(
      'Confirm all orders',
      name: 'oc_order_confirm_all',
      desc: '',
      args: [],
    );
  }

  /// `Status`
  String get oc_status {
    return Intl.message('Status', name: 'oc_status', desc: '', args: []);
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<VpUtilityLocalize> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'vi'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<VpUtilityLocalize> load(Locale locale) =>
      VpUtilityLocalize.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
