enum VpUtilityRouter {
  recommendationList('/recommendationList'),
  securitiesStatement('/securitiesStatement'),
  profitHistory('/profitHistory'),
  derivativeStatement('/derivativeStatement'),
  stockFilter('/stockFilter'),
  searchStock('/searchStock'),
  listEmonie('/listEmonie'),
  stockAlert('/stockAlert'),
  event('/event'),
  stockRight('/stockRight'),
  stockTransfer('/stockTransfer'),
  ranking('/ranking'),
  recommendation('/recommendation'),
  modelPortfolio('/modelPortfolio'),
  advancePayment('/advancePayment'),
  financialServicePackage('/financialServicePackage'),
  orderConfirm('/orderConfirm'),
  marketRouter('/marketRouter');

  final String routeName;

  const VpUtilityRouter(this.routeName);
}
