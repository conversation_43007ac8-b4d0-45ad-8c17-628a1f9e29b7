import 'package:flutter/material.dart';
import 'package:vp_utility/model/mixin/input_formatter.dart';
import 'package:vp_utility/utils/error_state.dart';
import 'package:vp_utility/utils/input_controller.dart';

abstract class InputFieldState extends InputController with InputFormatter {
  InputFieldState({super.controller, super.focusNode});

  void fill(data) {
    if (data == null || data.toString() == controller.text) return;
    TextEditingValue newValue =
        controller.value.copyWith(text: data.toString());
    for (final formatter in inputFormatters) {
      newValue = formatter.formatEditUpdate(controller.value, newValue);
    }
    controller.value = newValue;
  }

  String get hintText => '';

  bool get enabled => true;

  dynamic get value => null;

  TextAlign get textAlign => TextAlign.center;

  EdgeInsets get contentPadding => const EdgeInsets.all(16);

  TextInputType? get keyboardType => null;

  Widget? prefix(Color color) => null;

  Widget? suffix(Color color) => null;

  bool get clear => false;

  int? get maxLines => 1;

  double get errorHeight => 20.0;

  ErrorState? get emptyError => null;

  ErrorState? get error => null;

  ErrorState? get _error => isEmpty ? emptyError : error;

  String get errorMessage => _error?.toString() ?? '';

  bool get isEmpty => controller.text.trim().isEmpty;

  bool get isError => _error != null;

  bool get isDone => !isError && !isEmpty;

  bool get isDoneOrEmpty => isDone || isEmpty;
}
