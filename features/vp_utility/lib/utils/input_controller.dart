import 'package:flutter/material.dart';

abstract class InputController {
  final TextEditingController controller;
  final FocusNode focusNode;
  late Function() _textListener = () {};
  late Function() _focusListener = () {};

  InputController({TextEditingController? controller, FocusNode? focusNode})
      : controller = controller ?? TextEditingController(),
        focusNode = focusNode ?? FocusNode();

  void addListener({
    Function(String text)? text,
    Function(bool focus)? focus,
  }) {
    _textListener = text == null ? () {} : () => text(controller.text);
    _focusListener = focus == null ? () {} : () => focus(focusNode.hasFocus);
    controller.addListener(_textListener);
    focusNode.addListener(_focusListener);
  }

  void removeListener() {
    controller.removeListener(_textListener);
    focusNode.removeListener(_focusListener);
  }
}
