name: vp_utility
description: "A new Flutter package project."
version: 0.0.1
homepage:

environment:
  sdk: ^3.7.0
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  vp_design_system:
    path: ../../library/vp_design_system
  vp_common:
    path: ../../library/vp_common
  vp_core:
    path: ../../library/vp_core
  vp_stock_common:
    path: ../../library/vp_stock_common

  shimmer:
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  url_launcher: ^6.3.1
  flutter_widget_from_html: ^0.16.0
  equatable:
  tuple: 2.0.2
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner:
  custom_lint:
  json_serializable:
  retrofit_generator:
  flutter_gen_runner:
  intl_utils:
  freezed:
 

flutter_gen:
  output: lib/generated/
  line_length: 80
  assets:
    outputs:
      class_name: VpUtilityAssets
      package_parameter_enabled: true

    # Optional
  integrations:
    image: true
    flutter_svg: true
    
 
flutter:
  uses-material-design: true
  assets:
    - assets/icons/
    - assets/images/

flutter_intl:
  enabled: true
  class_name: VpUtilityLocalize