import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_settings/generated/l10n.dart';

import '../../../../../cubit/customer_info/general_info/general_info_bloc.dart';
import 'info_widget.dart';
import 'title_info_widget.dart';

class StaffInfoWidget extends StatelessWidget {
  const StaffInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GeneralInfoBloc, GeneralInfoState>(
      builder:
          (context, state) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TitleInfoWidget(
                title: VPSettingsLocalize.current.settings_caringStaff,
                edit: false,
                // onPressed: () =>
                //     context.read<CustomerInfoBloc>().onChangeVisibleStaffInfo(),
              ),
              // context.read<CustomerInfoBloc>().visibleStaffInfo
              //     ?
              Column(
                children: [
                  InfoWidget(
                    desc: VPSettingsLocalize.current.settings_staffName,
                    info: context.read<GeneralInfoBloc>().staffInfo?.fullName,
                  ),
                  InfoWidget(
                    desc: VPSettingsLocalize.current.settings_phoneNumber,
                    info: context.read<GeneralInfoBloc>().staffInfo?.mobile,
                  ),
                  // InfoWidget(
                  //   desc: getSettingsLang( SettingsKeyLang.email),
                  //   info: context.read<CustomerInfoBloc>().info!.email,
                  // ),
                  // InfoWidget(
                  //   desc: getSettingsLang( SettingsKeyLang.branch),
                  //   info: context.read<CustomerInfoBloc>().info!.brname,
                  // ),
                ],
              ),
              // : TextButton(
              //     onPressed: () {},//=> Routes.push(context, AddCaringStaffPage()),
              //     child: Row(
              //       children: [
              //         Icon(
              //           Icons.add,
              //           color: Theme.of(context).primaryColor,
              //           size: 10,
              //         ),
              //         const SizedBox(width: 4,),
              //         Expanded(
              //             child: Text(
              //               getSettingsLang( SettingsKeyLang.addStaff),
              //           style: TextStyleUtils.text14Weight600
              //               .copyWith(color: Theme.of(context).primaryColor),
              //         ))
              //       ],
              //     ))
            ],
          ),
    );
  }
}
