import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_settings/generated/l10n.dart';

class AddBankNoteView extends StatelessWidget {
  const AddBankNoteView({super.key});

  final urlRegisterVPBank = 'https://taikhoan.vpbank.com.vn/';

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // RichText(
        //   textAlign: TextAlign.start,
        //   text: TextSpan(
        //     text: getAccountLang(AccountKeyLang.vpBankAccount),
        //     style: vpTextStyle.body14?
        //         .copyWith(color: ColorUtils.gray500),
        //     children: [
        //       TextSpan(
        //           recognizer: TapGestureRecognizer()
        //             ..onTap = () async {
        //               SignUpTracking().signupRegisterBankAccount();
        //               if (await canLaunch(urlRegisterVPBank)) {
        //                 await launch(urlRegisterVPBank);
        //               }
        //             },
        //           text: ' ${getAccountLang(AccountKeyLang.registerNow)}',
        //           style: TextStyleUtils.text14Weight600
        //               .copyWith(color: ColorUtils.primary)),
        //     ],
        //   ),
        // ),
        // const SizedBox(height: 8,),
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '${VPSettingsLocalize.current.settings_note} : ',
                style: vpTextStyle.body14.copyColor(themeData.gray500),
              ),
              TextSpan(
                text: VPSettingsLocalize.current.settings_signUpBankNote,
                style: vpTextStyle.body14.copyColor(themeData.gray500),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
