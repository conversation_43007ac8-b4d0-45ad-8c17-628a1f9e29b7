import 'package:flutter/material.dart';
import 'package:vp_loyalty/model/loyalty/model/rank_details_entity.dart';

import 'image_rank_card.dart';

class CustomerCareRankCardView extends StatelessWidget {
  const CustomerCareRankCardView({
    Key? key,
    this.paddingBottom,
    this.data,
    this.width,
    this.height,
  }) : super(key: key);

  final double? paddingBottom;
  final double? width;
  final double? height;
  final RankDetailsEntity? data;

  @override
  Widget build(BuildContext context) {
    final width = this.width ?? MediaQuery.of(context).size.width - (16 * 2);
    final height = this.height ?? 0.58 * width;

    return SizedBox(
      height: height,
      width: width,
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: ImageRankCard(
              imageSource: data?.rankImage ?? '',
              height: height,
              width: width,
            ),
          ),
          // if (data?.isCurrentRank ?? false)
          //   Padding(
          //     padding:
          //         EdgeInsets.only(bottom: paddingBottom ?? 32),
          //     child: Align(
          //         alignment: Alignment.bottomCenter,
          //         child: Text(
          //             '${getCustomerCareLang(context, CustomerCareKeyLang.expireDate)} ${data?.expireDate}',
          //             style: TextStyleUtils.text12Weight500
          //                 .copyWith(color: ColorUtils.white))),
          //   )
        ],
      ),
    );
  }
}
