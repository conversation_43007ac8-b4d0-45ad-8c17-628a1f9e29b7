import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_loyalty/vp_loyalty.dart';

import '../../../gen/assets.gen.dart';

class ShareReferalGift extends StatelessWidget {
  const ShareReferalGift({Key? key, required this.title}) : super(key: key);

  final String title;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: const [
          BoxShadow(
            color: Colors.black26,
            offset: Offset(0, 4),
            blurRadius: 5.0,
          ),
        ],
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [themeData.primary.withOpacity(0.5), themeData.primary],
        ),
        color: themeData.primary,
        borderRadius: BorderRadius.circular(20),
      ),
      child: <PERSON>zedBox(
        height: 26,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(width: 8),
            SvgPicture.asset(
              Assets.icons.icGift,
              color: themeData.textEnable,
              width: 16,
              package: vpLoyalty,
            ),
            SizedBox(width: 4),
            Padding(
              padding: const EdgeInsets.only(bottom: 2),
              child: Text(
                title,
                style: vpTextStyle.captionMedium.copyColor(
                  themeData.textEnable,
                ),
              ),
            ),
            SizedBox(width: 8),
          ],
        ),
        // child: ElevatedButton(
        //   style: ButtonStyle(
        //     shape: MaterialStateProperty.all<RoundedRectangleBorder>(
        //       RoundedRectangleBorder(
        //         borderRadius: BorderRadius.circular(12),
        //       ),
        //     ),
        //     elevation: MaterialStateProperty.all<double>(1),
        //     backgroundColor: MaterialStateProperty.all(Colors.transparent),
        //   ),
        //   onPressed: () {},
        //   child: Padding(
        //     padding: const EdgeInsets.symmetric(vertical: 2),
        //     child: IntrinsicWidth(
        //       child: Row(
        //         children: [
        //           SvgPicture.asset(
        //             SettingsKeyAssets.icGift,
        //             color: ColorUtils.white,
        //             width: 16,
        //           ),
        //           padH8,
        //           Padding(
        //             padding: const EdgeInsets.only(top: 2),
        //             child: Text(title,
        //                 style: TextStyleUtils.text12Weight500
        //                     .copyWith(color: ColorUtils.white)),
        //           ),
        //         ],
        //       ),
        //     ),
        //   ),
        // ),
      ),
    );
  }
}
