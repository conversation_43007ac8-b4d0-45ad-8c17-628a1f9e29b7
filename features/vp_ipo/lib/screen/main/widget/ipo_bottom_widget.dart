import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/generated/assets.gen.dart';

class IpoBottomWidget extends StatelessWidget {
  const IpoBottomWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final autoSizeGroup = AutoSizeGroup();
    return BlocBuilder<IpoMainCubit, IpoMainState>(
      buildWhen: (a, b) {
        return a.typePage != b.typePage;
      },
      builder: (context, state) {
        final typePage = state.typePage;
        return DecoratedBox(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: themeData.boxShadow.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 1,
              ),
            ],
          ),
          child: BottomAppBar(
            color: themeData.bgBottomBar,
            notchMargin: 8,
            shape: const CircularNotchedRectangle(),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  flex: 2,
                  child: NavItem(
                    package: 'vp_ipo',
                    isSelected: typePage == TypePageIpo.main,
                    selectIcon: VpIpoAssets.icons.icHome.path,
                    unselectIcon: VpIpoAssets.icons.icHome.path,
                    title: 'Thông tin',
                    onTap: () {
                      context.read<IpoMainCubit>().changePage(0);
                    },
                    autoSizeGroup: autoSizeGroup,
                    haveHeadDivider: true,
                    marginHeadDivider: const EdgeInsets.only(right: 8),
                  ),
                ),

                const Spacer(),
                Expanded(
                  flex: 2,
                  child: NavItem(
                    package: 'vp_ipo',
                    selectIcon: VpIpoAssets.icons.icHistory.path,
                    unselectIcon: VpIpoAssets.icons.icHistory2.path,
                    isSelected: typePage == TypePageIpo.history,
                    title: 'Tra cứu',
                    onTap: () {
                      context.read<IpoMainCubit>().changePage(1);
                    },
                    autoSizeGroup: autoSizeGroup,
                    haveHeadDivider: true,
                    marginHeadDivider: const EdgeInsets.only(left: 18),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
