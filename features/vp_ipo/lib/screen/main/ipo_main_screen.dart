import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/cubit/ipo_main/ipo_main_cubit.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/screen/ipo_history/ipo_history_screen.dart';
import 'package:vp_ipo/screen/ipo_home/ipo_home_screen.dart';

class IpoMainScreen extends StatefulWidget {
  const IpoMainScreen({super.key});

  @override
  State<IpoMainScreen> createState() => _IpoMainScreenState();
}

class _IpoMainScreenState extends State<IpoMainScreen> {
  get initialPage => 0;

  late final PageController _pageController = PageController(
    initialPage: initialPage,
  );

  final _children = <Widget>[const IpoHomeScreen(), const IpoHistoryScreen()];

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => IpoMainCubit(initialPage: initialPage),
      child: Scaffold(
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            context.push('/placeOrder');
          },
          backgroundColor: themeData.primary,
          child: SvgPicture.asset(VpIpoAssets.icons.icSetCommand.path),
        ),
        resizeToAvoidBottomInset: true,
        //  bottomNavigationBar: const IpoBottomWidget(),
        body: BlocConsumer<IpoMainCubit, IpoMainState>(
          listener: (context, state) {
            _pageController.jumpToPage(state.page);
          },
          builder: (context, state) {
            return PageView(
              physics: const NeverScrollableScrollPhysics(),
              controller: _pageController,
              onPageChanged: (index) {
                context.read<IpoMainCubit>().changePage(index);
              },
              children: _children,
            );
          },
        ),
      ),
    );
  }
}
