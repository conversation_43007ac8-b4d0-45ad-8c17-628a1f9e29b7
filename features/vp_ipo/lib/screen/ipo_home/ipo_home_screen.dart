import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/gen/assets.gen.dart' as asset;
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_ipo/generated/assets.gen.dart';
import 'package:vp_ipo/screen/ipo_home/widget/banner_widget.dart';
import 'package:vp_ipo/screen/ipo_home/widget/count_down_widget.dart';

class IpoHomeScreen extends StatefulWidget {
  const IpoHomeScreen({super.key});

  @override
  State<IpoHomeScreen> createState() => _IpoHomeScreenState();
}

class _IpoHomeScreenState extends State<IpoHomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: Image.asset(
              width: double.infinity,
              package: 'vp_ipo',
              VpIpoAssets.images.bgIpoMain.path,
              fit: BoxFit.cover,
            ),
          ),
          Scaffold(
            backgroundColor: Colors.transparent,
            appBar: VPAppBar.layer(
              leading: IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                icon: asset.Assets.icons.appbar.icBack.svg(
                  colorFilter: ColorFilter.mode(
                    themeData.white,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              title: '',
              backgroundColor: Colors.transparent,
              actions: [
                VpIpoAssets.icons.icPhone.svg(),
                const SizedBox(width: 12),
                VpIpoAssets.icons.icHelp.svg(),
                const SizedBox(width: 12),
                VpIpoAssets.icons.icNotification.svg(),
                const SizedBox(width: 16),
              ],
            ),
            body: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 110),
                    _infoTimer(),
                    const SizedBox(height: 12),
                    _buildIPOTimeline(),
                    const SizedBox(height: 12),
                    const BannerSlider(),
                    const SizedBox(height: 12),
                    _assetWidget(),
                    const SizedBox(height: 12),
                    _aboutVpbs(),
                    const SizedBox(height: 12),
                    _aboutIPO(),
                    const SizedBox(height: 100),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _infoTimer() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        //    height: 152,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: VpIpoAssets.images.cardInfo.provider(),
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Stack(
                  children: [
                    VpIpoAssets.icons.tagInfo.svg(),
                    Positioned(
                      left: 16,
                      top: 8,
                      child: VpIpoAssets.icons.logo.svg(),
                    ),
                  ],
                ),
                Expanded(
                  child: CountdownTimerWidget(
                    targetTime: DateTime.now().add(const Duration(days: 30)),
                  ),
                ),
                const SizedBox(width: 12),
              ],
            ),
            const SizedBox(height: 8),
            _infoStock(),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Expanded(
                child: Center(
                  child: SizedBox(
                    child: VpsButton.primaryXsSmall(
                      title: "Đăng ký mua",
                      width: double.infinity,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 12),
          ],
        ),
      ),
    );
  }

  Widget _infoStock() {
    return Row(
      children: [
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Giá chào bán",
                style: vpTextStyle.body14.copyColor(vpColor.textSecondary),
              ),
              Text(
                "32,000 VND/CP",
                style: vpTextStyle.subtitle14?.copyWith(
                  color: vpColor.textPrimary,
                  fontWeight: FontWeight.w800,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "SL chào bán",
                style: vpTextStyle.body14.copyColor(vpColor.textSecondary),
              ),
              Text(
                "291,100,000 CP",
                style: vpTextStyle.subtitle14?.copyWith(
                  color: vpColor.textPrimary,
                  fontWeight: FontWeight.w800,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildIPOTimeline() {
    Widget itemValue(String title, String value, bool isSuccess) {
      return Expanded(
        child: Column(
          children: [
            Text(
              title,
              style: vpTextStyle.captionSemiBold.copyColor(
                isSuccess ? vpColor.textBrand : const Color(0xffA5B0C2),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: vpTextStyle.caption2Medium.copyColor(
                isSuccess ? vpColor.textWhite : const Color(0xffA5B0C2),
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  Expanded(child: const SizedBox()),
                  VpIpoAssets.icons.icStepSelected.svg(),
                  Expanded(child: DashWidget(color: const Color(0xff123A4A))),
                ],
              ),
            ),
            Expanded(
              child: Row(
                children: [
                  Expanded(child: DashWidget(color: const Color(0xff123A4A))),
                  VpIpoAssets.icons.icStep.svg(),
                  Expanded(child: DashWidget(color: const Color(0xff123A4A))),
                ],
              ),
            ),

            Expanded(
              child: Row(
                children: [
                  Expanded(child: DashWidget(color: const Color(0xff123A4A))),
                  VpIpoAssets.icons.icStep.svg(),
                  Expanded(child: DashWidget(color: const Color(0xff123A4A))),
                ],
              ),
            ),
            Expanded(
              child: Row(
                children: [
                  Expanded(child: DashWidget(color: const Color(0xff123A4A))),
                  VpIpoAssets.icons.icStep.svg(),
                  Expanded(child: const SizedBox()),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            itemValue("Đăng ký", "19/08 - 08/09", true),
            itemValue("Phân bổ", "09/09  - 10/09", false),
            itemValue("Thanh toán", "11/09 - 29/09", false),
            itemValue("Hoàn tất", "30/09", false),
          ],
        ),
      ],
    );
  }

  Widget _assetWidget() {
    Widget infoChild(String title, String value) {
      return Expanded(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: vpTextStyle.subtitle14.copyColor(vpColor.textWhite),
            ),
            Text(
              value,
              style: vpTextStyle.body14?.copyWith(
                color: vpColor.textBrand,
                fontWeight: FontWeight.w800,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xff1441524d).withValues(alpha: 0.3),
        border: Border.all(color: const Color(0xff1441524d)),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          const BoxShadow(color: Color(0xff1441524d)),
          const BoxShadow(
            color: Color(0xff1441524d),
            spreadRadius: 8,
            blurRadius: 8,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Tài sản nắm giữ",
            style: vpTextStyle.subtitle16.copyColor(vpColor.textWhite),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              infoChild("Số lượng đặt mua", "10,000 CP"),
              infoChild("Giá chào bán", "32,000 VNĐ/CP"),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              infoChild("Tiền cọc", "32,000,000 VNĐ"),
              infoChild("Tổng giá trị", "320,000,000 VNĐ"),
            ],
          ),
        ],
      ),
    );
  }

  Widget _aboutVpbs() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Giới thiệu về VPBankS",
          style: vpTextStyle.subtitle16.copyColor(vpColor.textWhite),
        ),
        const SizedBox(height: 8),
        Text(
          "VPBankS là thành viên chiến lược trong hệ sinh thái tài chính Ngân hàng TMCP Việt Nam Thịnh Vượng VPBank ",
          style: vpTextStyle.captionMedium.copyColor(Color(0xffA8B0BD)),
        ),
        const SizedBox(height: 20),
        Row(
          children: [
            Expanded(
              child: Stack(
                children: [
                  VpIpoAssets.images.aboutVpbs1.image(),
                  Positioned(
                    top: 0,
                    right: 0,
                    child: VpIpoAssets.icons.aboutVpbsIcon1.svg(),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Stack(
                children: [
                  VpIpoAssets.images.aboutVpbs2.image(),
                  Positioned(
                    top: 0,
                    right: 0,
                    child: VpIpoAssets.icons.aboutVpbsIcon2.svg(),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: Stack(
                children: [
                  VpIpoAssets.images.aboutVpbs3.image(),
                  Positioned(
                    top: 0,
                    right: 0,
                    child: VpIpoAssets.icons.aboutVpbsIcon3.svg(),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Stack(
                children: [
                  VpIpoAssets.images.aboutVpbs4.image(),
                  Positioned(
                    top: 0,
                    right: 0,
                    child: VpIpoAssets.icons.aboutVpbsIcon4.svg(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _aboutIPO() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Giới thiệu về IPO",
          style: vpTextStyle.subtitle16.copyColor(vpColor.textWhite),
        ),
        const SizedBox(height: 8),
        Text(
          "VPBankS là thành viên chiến lược trong hệ sinh thái tài chính Ngân hàng TMCP Việt Nam Thịnh Vượng VPBank",
          style: vpTextStyle.captionMedium.copyColor(Color(0xffA8B0BD)),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}

class DashWidget extends StatelessWidget {
  const DashWidget({super.key, this.height = 1, this.color = Colors.black});
  final double height;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final boxWidth = constraints.constrainWidth();
        const dashWidth = 4.0;
        final dashHeight = height;
        final dashCount = (boxWidth / (2 * dashWidth)).floor();
        return Flex(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          direction: Axis.horizontal,
          children: List.generate(dashCount, (_) {
            return SizedBox(
              width: dashWidth,
              height: dashHeight,
              child: DecoratedBox(decoration: BoxDecoration(color: color)),
            );
          }),
        );
      },
    );
  }
}
