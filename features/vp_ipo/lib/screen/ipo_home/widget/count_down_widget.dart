import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/generated/assets.gen.dart';

// class CountDownWidget extends StatefulWidget {
//   const CountDownWidget({super.key});

//   @override
//   State<CountDownWidget> createState() => _CountDownWidgetState();
// }

// class _CountDownWidgetState extends State<CountDownWidget> {
//   @override
//   void initState() {
//     super.initState();
//     _startTimer();
//   }

//   int _counter = 1000;
//   late Timer _timer;

//   void _startTimer() {
//     _counter = 10;
//     _timer = Timer.periodic(Duration(seconds: 1), (timer) {
//       setState(() {
//         _counter--;
//       });
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       height: 20,
//       //   width: 160,
//       decoration: BoxDecoration(
//         image: DecorationImage(
//           image: VpIpoAssets.images.bgTimeCountDown.provider(),
//           //    fit: BoxFit.fitWidth,
//         ),
//       ),
//     );
//     return VpIpoAssets.icons.countDownBg.svg(height: 20);
//     return SizedBox(
//       child: Text(
//         '$_counter',
//         style: TextStyle(fontSize: 48, fontWeight: FontWeight.bold),
//       ),
//     );
//   }
// }
class CountdownTimerWidget extends StatefulWidget {
  final DateTime targetTime;

  const CountdownTimerWidget({super.key, required this.targetTime});

  @override
  State<CountdownTimerWidget> createState() => _CountdownTimerWidgetState();
}

class _CountdownTimerWidgetState extends State<CountdownTimerWidget> {
  late Timer _timer;
  Duration _remaining = Duration.zero;

  @override
  void initState() {
    super.initState();
    _updateRemaining();
    _timer = Timer.periodic(
      const Duration(seconds: 1),
      (_) => _updateRemaining(),
    );
  }

  void _updateRemaining() {
    final now = DateTime.now();
    setState(() {
      _remaining = widget.targetTime.difference(now);
      if (_remaining.isNegative) {
        _remaining = Duration.zero; // stop at 0
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  String _formatDuration(Duration d) {
    final days = d.inDays;
    final hours = d.inHours % 24;
    final minutes = d.inMinutes % 60;

    if (days > 0) {
      return "$days d $hours h $minutes m";
    }
    return "$hours h $minutes m ";
  }

  Widget _dayWidget(int value, String des) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '$value',
          style: vpTextStyle.captionSemiBold.copyColor(vpColor.textWhite),
        ),
        const SizedBox(width: 2),
        Text(
          des,
          style: vpTextStyle.captionRegular.copyColor(vpColor.textWhite),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final days = _remaining.inDays;
    final hours = _remaining.inHours % 24;
    final minutes = _remaining.inMinutes % 60;

    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: VpIpoAssets.images.bgTimeCountDown.provider(),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _dayWidget(days, 'ngày'),
          const SizedBox(width: 4),
          Text(':', style: vpTextStyle.subtitle14.copyColor(vpColor.textWhite)),
          const SizedBox(width: 4),
          _dayWidget(hours, 'giờ'),
          Text(':', style: vpTextStyle.subtitle14.copyColor(vpColor.textWhite)),
          const SizedBox(width: 4),
          _dayWidget(minutes, 'phút'),
        ],
      ),
    );
  }
}
