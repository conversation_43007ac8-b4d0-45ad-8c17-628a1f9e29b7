// dart format width=80

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/about_vpbs_icon1.svg
  SvgGenImage get aboutVpbsIcon1 =>
      const SvgGenImage('assets/icons/about_vpbs_icon1.svg');

  /// File path: assets/icons/about_vpbs_icon2.svg
  SvgGenImage get aboutVpbsIcon2 =>
      const SvgGenImage('assets/icons/about_vpbs_icon2.svg');

  /// File path: assets/icons/about_vpbs_icon3.svg
  SvgGenImage get aboutVpbsIcon3 =>
      const SvgGenImage('assets/icons/about_vpbs_icon3.svg');

  /// File path: assets/icons/about_vpbs_icon4.svg
  SvgGenImage get aboutVpbsIcon4 =>
      const SvgGenImage('assets/icons/about_vpbs_icon4.svg');

  /// File path: assets/icons/count_down_bg.svg
  SvgGenImage get countDownBg =>
      const SvgGenImage('assets/icons/count_down_bg.svg');

  /// File path: assets/icons/ic_ask.svg
  SvgGenImage get icAsk => const SvgGenImage('assets/icons/ic_ask.svg');

  /// File path: assets/icons/ic_help.svg
  SvgGenImage get icHelp => const SvgGenImage('assets/icons/ic_help.svg');

  /// File path: assets/icons/ic_history.svg
  SvgGenImage get icHistory => const SvgGenImage('assets/icons/ic_history.svg');

  /// File path: assets/icons/ic_history2.svg
  SvgGenImage get icHistory2 =>
      const SvgGenImage('assets/icons/ic_history2.svg');

  /// File path: assets/icons/ic_home.svg
  SvgGenImage get icHome => const SvgGenImage('assets/icons/ic_home.svg');

  /// File path: assets/icons/ic_intro.svg
  SvgGenImage get icIntro => const SvgGenImage('assets/icons/ic_intro.svg');

  /// File path: assets/icons/ic_notification.svg
  SvgGenImage get icNotification =>
      const SvgGenImage('assets/icons/ic_notification.svg');

  /// File path: assets/icons/ic_phone.svg
  SvgGenImage get icPhone => const SvgGenImage('assets/icons/ic_phone.svg');

  /// File path: assets/icons/ic_sell.svg
  SvgGenImage get icSell => const SvgGenImage('assets/icons/ic_sell.svg');

  /// File path: assets/icons/ic_set_command.svg
  SvgGenImage get icSetCommand =>
      const SvgGenImage('assets/icons/ic_set_command.svg');

  /// File path: assets/icons/ic_step.svg
  SvgGenImage get icStep => const SvgGenImage('assets/icons/ic_step.svg');

  /// File path: assets/icons/ic_step_selected.svg
  SvgGenImage get icStepSelected =>
      const SvgGenImage('assets/icons/ic_step_selected.svg');

  /// File path: assets/icons/logo.svg
  SvgGenImage get logo => const SvgGenImage('assets/icons/logo.svg');

  /// File path: assets/icons/tag_info.svg
  SvgGenImage get tagInfo => const SvgGenImage('assets/icons/tag_info.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    aboutVpbsIcon1,
    aboutVpbsIcon2,
    aboutVpbsIcon3,
    aboutVpbsIcon4,
    countDownBg,
    icAsk,
    icHelp,
    icHistory,
    icHistory2,
    icHome,
    icIntro,
    icNotification,
    icPhone,
    icSell,
    icSetCommand,
    icStep,
    icStepSelected,
    logo,
    tagInfo,
  ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/about_vpbs1.png
  AssetGenImage get aboutVpbs1 =>
      const AssetGenImage('assets/images/about_vpbs1.png');

  /// File path: assets/images/about_vpbs2.png
  AssetGenImage get aboutVpbs2 =>
      const AssetGenImage('assets/images/about_vpbs2.png');

  /// File path: assets/images/about_vpbs3.png
  AssetGenImage get aboutVpbs3 =>
      const AssetGenImage('assets/images/about_vpbs3.png');

  /// File path: assets/images/about_vpbs4.png
  AssetGenImage get aboutVpbs4 =>
      const AssetGenImage('assets/images/about_vpbs4.png');

  /// File path: assets/images/banner_stock_guru.png
  AssetGenImage get bannerStockGuru =>
      const AssetGenImage('assets/images/banner_stock_guru.png');

  /// File path: assets/images/bg_ipo_main.png
  AssetGenImage get bgIpoMain =>
      const AssetGenImage('assets/images/bg_ipo_main.png');

  /// File path: assets/images/bg_time_count_down.png
  AssetGenImage get bgTimeCountDown =>
      const AssetGenImage('assets/images/bg_time_count_down.png');

  /// File path: assets/images/card_info.png
  AssetGenImage get cardInfo =>
      const AssetGenImage('assets/images/card_info.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    aboutVpbs1,
    aboutVpbs2,
    aboutVpbs3,
    aboutVpbs4,
    bannerStockGuru,
    bgIpoMain,
    bgTimeCountDown,
    cardInfo,
  ];
}

class VpIpoAssets {
  const VpIpoAssets._();

  static const String package = 'vp_ipo';

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
    this.animation,
  });

  final String _assetName;

  static const String package = 'vp_ipo';

  final Size? size;
  final Set<String> flavors;
  final AssetGenImageAnimation? animation;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
  }) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_ipo/$_assetName';
}

class AssetGenImageAnimation {
  const AssetGenImageAnimation({
    required this.isAnimation,
    required this.duration,
    required this.frames,
  });

  final bool isAnimation;
  final Duration duration;
  final int frames;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  static const String package = 'vp_ipo';

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    _svg.ColorMapper? colorMapper,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
        colorMapper: colorMapper,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => 'packages/vp_ipo/$_assetName';
}
