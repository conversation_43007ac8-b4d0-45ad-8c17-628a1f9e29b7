import 'package:equatable/equatable.dart';
import 'package:vp_core/vp_core.dart';

part 'ipo_main_state.dart';

enum TypePageIpo { main, history }

class IpoMainCubit extends Cubit<IpoMainState> {
  IpoMainCubit({required int initialPage})
    : super(
        IpoMainState(
          typePage: TypePageIpo.values[initialPage],
          page: initialPage,
        ),
      );

  void changePage(int index) {
    emit(
      state.copyWith(typePage: TypePageIpo.values[index], page: index),
    );
  }
}
