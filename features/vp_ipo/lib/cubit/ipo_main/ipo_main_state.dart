part of 'ipo_main_cubit.dart';

class IpoMainState extends Equatable {
  const IpoMainState({
    this.typePage = TypePageIpo.main,
    this.page = 0,
  });
  final TypePageIpo typePage;
  final int page;

  @override
  List<Object?> get props => [typePage, page];

  IpoMainState copyWith({TypePageIpo? typePage, int? page}) {
    return IpoMainState(
      typePage: typePage ?? this.typePage,
      page: page ?? this.page,
    );
  }
}
