import 'package:flutter/widgets.dart';

import 'package:vp_common/vp_common.dart' hide AppLocalizationDelegate;
import 'package:vp_core/vp_core.dart';
import 'package:vp_ipo/generated/l10n.dart';
import 'package:vp_ipo/router/ipo_router.dart';
import 'package:vp_ipo/screen/main/ipo_main_screen.dart';
import 'generated/intl/messages_all.dart';

class IpoModule implements Module {
  final assetShellKey = GlobalKey<NavigatorState>();

  @override
  void injectServices(GetIt service) {
   
  }

  @override
  List<RouteBase> router() {
    return [
      GoRoute(
        path: VPIpoRouter.ipoMain.routeName,
        builder:
            (context, state) =>
                IpoMainScreen(),
      ),
    
    ];
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [MultiLocalizationsDelegate.delegate];
  }

  @override
  String modulePath() {
    return 'vp_ipo';
  }
}

class MultiLocalizationsDelegate extends AppLocalizationDelegate {
  const MultiLocalizationsDelegate();

  static const AppLocalizationDelegate delegate = MultiLocalizationsDelegate();

  @override
  Future<VPIpoLocalize> load(Locale locale) {
    return MultipleLocalizations.load(
      initializeMessages,
      locale,
      (l) => VPIpoLocalize.load(locale),
      setDefaultLocale: true,
    );
  }
}
