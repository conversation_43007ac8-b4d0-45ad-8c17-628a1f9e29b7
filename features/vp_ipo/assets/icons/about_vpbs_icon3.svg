<svg width="64" height="56" viewBox="0 0 64 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_159273_52052)">
<g filter="url(#filter0_f_159273_52052)">
<ellipse cx="31" cy="46" rx="25" ry="1" fill="black" fill-opacity="0.3"/>
</g>
<rect x="10" y="25" width="11" height="21" rx="2" fill="url(#paint0_linear_159273_52052)"/>
<rect x="26" y="18" width="11" height="28" rx="2" fill="url(#paint1_linear_159273_52052)"/>
<rect x="42" y="7" width="11" height="39" rx="2" fill="url(#paint2_linear_159273_52052)"/>
<foreignObject x="4" y="18" width="19" height="29"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_1_159273_52052_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_ii_159273_52052)" data-figma-bg-blur-radius="4">
<rect x="8" y="22" width="11" height="21" rx="2" fill="#7EFF18" fill-opacity="0.2"/>
</g>
<foreignObject x="20" y="11" width="19" height="36"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_2_159273_52052_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter2_ii_159273_52052)" data-figma-bg-blur-radius="4">
<rect x="24" y="15" width="11" height="28" rx="2" fill="#7EFF18" fill-opacity="0.2"/>
</g>
<foreignObject x="36" y="0" width="19" height="47"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_3_159273_52052_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter3_ii_159273_52052)" data-figma-bg-blur-radius="4">
<rect x="40" y="4" width="11" height="39" rx="2" fill="#7EFF18" fill-opacity="0.2"/>
</g>
<g filter="url(#filter4_d_159273_52052)">
<path d="M58.5 27C58.5 27.8284 57.8284 28.4999 57 28.5C56.1716 28.5 55.5 27.8284 55.5 27V18.0273L40.6064 31.5674C39.7355 32.3591 38.4296 32.4358 37.4717 31.752L31.9434 27.8027L21.166 34.54C20.5749 34.9095 19.8562 35.0153 19.1836 34.832L3.10547 30.4473C2.30629 30.2293 1.83487 29.4047 2.05273 28.6055C2.27071 27.8063 3.09536 27.3349 3.89453 27.5527L19.7617 31.8799L30.6377 25.082C31.4957 24.5459 32.5927 24.5799 33.416 25.168L38.8857 29.0752L52.7197 16.5H44C43.1716 16.5 42.5 15.8284 42.5 15C42.5 14.1716 43.1716 13.5 44 13.5H58.5V27Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_f_159273_52052" x="2" y="41" width="58" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_159273_52052"/>
</filter>
<filter id="filter1_ii_159273_52052" x="4" y="18" width="19" height="29" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_159273_52052"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_159273_52052" result="effect2_innerShadow_159273_52052"/>
</filter>
<clipPath id="bgblur_1_159273_52052_clip_path" transform="translate(-4 -18)"><rect x="8" y="22" width="11" height="21" rx="2"/>
</clipPath><filter id="filter2_ii_159273_52052" x="20" y="11" width="19" height="36" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_159273_52052"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_159273_52052" result="effect2_innerShadow_159273_52052"/>
</filter>
<clipPath id="bgblur_2_159273_52052_clip_path" transform="translate(-20 -11)"><rect x="24" y="15" width="11" height="28" rx="2"/>
</clipPath><filter id="filter3_ii_159273_52052" x="36" y="0" width="19" height="47" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_159273_52052"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_159273_52052" result="effect2_innerShadow_159273_52052"/>
</filter>
<clipPath id="bgblur_3_159273_52052_clip_path" transform="translate(-36 0)"><rect x="40" y="4" width="11" height="39" rx="2"/>
</clipPath><filter id="filter4_d_159273_52052" x="-2.00049" y="13.5" width="64.5005" height="29.42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_159273_52052"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_159273_52052" result="shape"/>
</filter>
<linearGradient id="paint0_linear_159273_52052" x1="16" y1="25" x2="16" y2="46" gradientUnits="userSpaceOnUse">
<stop stop-color="#4DDD36"/>
<stop offset="1" stop-color="#188530"/>
</linearGradient>
<linearGradient id="paint1_linear_159273_52052" x1="32" y1="18" x2="32" y2="46" gradientUnits="userSpaceOnUse">
<stop stop-color="#4DDD36"/>
<stop offset="1" stop-color="#188530"/>
</linearGradient>
<linearGradient id="paint2_linear_159273_52052" x1="48" y1="7" x2="48" y2="46" gradientUnits="userSpaceOnUse">
<stop stop-color="#4DDD36"/>
<stop offset="1" stop-color="#188530"/>
</linearGradient>
<clipPath id="clip0_159273_52052">
<rect width="64" height="64" fill="white" transform="translate(0 -8)"/>
</clipPath>
</defs>
</svg>
