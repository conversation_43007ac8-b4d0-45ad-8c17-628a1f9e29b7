<svg width="62" height="56" viewBox="0 0 62 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_159273_52037)">
<circle cx="38.5" cy="32.5" r="17.5" fill="url(#paint0_linear_159273_52037)"/>
</g>
<foreignObject x="6" y="-1" width="50" height="50"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_159273_52037_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_dii_159273_52037)" data-figma-bg-blur-radius="3">
<circle cx="29" cy="22" r="20" fill="url(#paint1_linear_159273_52037)" fill-opacity="0.8" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter2_d_159273_52037)">
<path d="M30.834 11.3984C31.3201 11.509 31.7783 11.6618 32.207 11.8613C33.1303 12.2824 33.9001 12.8973 34.5156 13.7051C35.1393 14.5042 35.5922 15.4796 35.875 16.6309L32.3818 17.2754C32.2654 16.7254 32.0485 16.2445 31.7324 15.832C31.4163 15.4109 31.021 15.0846 30.5469 14.8525C30.0811 14.612 29.5612 14.483 28.9873 14.4658C28.4384 14.4487 27.9393 14.5296 27.4902 14.71C27.0494 14.8904 26.6959 15.1442 26.4297 15.4707C26.1718 15.7887 26.043 16.1498 26.043 16.5537C26.043 17.0006 26.2258 17.3873 26.5918 17.7139C26.9578 18.0318 27.5403 18.307 28.3389 18.5391L31.5078 19.4668C33.0966 19.9394 34.2407 20.5799 34.9395 21.3877C35.6465 22.1955 36 23.2657 36 24.5977C35.9999 25.7662 35.7126 26.7927 35.1387 27.6777C34.5647 28.5629 33.762 29.2545 32.7305 29.7529C32.1517 30.0349 31.5192 30.234 30.834 30.3564V32.6924H27.4404V30.3369C26.8238 30.215 26.2452 30.0268 25.7061 29.7666C24.6912 29.2682 23.8632 28.5674 23.2227 27.665C22.5822 26.7542 22.1747 25.6798 22 24.4424L25.3691 23.9014C25.5938 24.9153 26.064 25.7227 26.7793 26.3242C27.503 26.9257 28.3641 27.2266 29.3623 27.2266C30.3021 27.2265 31.0799 26.9993 31.6953 26.5439C32.3191 26.0886 32.6307 25.5256 32.6309 24.8555C32.6309 24.4001 32.4941 24.0256 32.2197 23.7334C31.9535 23.4326 31.5329 23.1881 30.959 22.999L26.5166 21.5811C23.9548 20.7647 22.6739 19.149 22.6738 16.7344C22.6738 15.6 22.9315 14.6158 23.4473 13.7822C23.9713 12.9487 24.7079 12.3082 25.6562 11.8613C26.1964 11.6042 26.7915 11.4236 27.4404 11.3145V9H30.834V11.3984Z" fill="url(#paint2_linear_159273_52037)"/>
</g>
<defs>
<filter id="filter0_i_159273_52037" x="21" y="14" width="35" height="36" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.848333 0 0 0 0 0.490619 0 0 0 0 0.0706942 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_159273_52037"/>
</filter>
<filter id="filter1_dii_159273_52037" x="6" y="-1" width="50" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="3"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.801667 0 0 0 0 0.533787 0 0 0 0 0.130271 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_159273_52037"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_159273_52037" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_159273_52037"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_159273_52037" result="effect3_innerShadow_159273_52037"/>
</filter>
<clipPath id="bgblur_0_159273_52037_clip_path" transform="translate(-6 1)"><circle cx="29" cy="22" r="20"/>
</clipPath><filter id="filter2_d_159273_52037" x="18" y="7" width="22" height="31.6924" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.975 0 0 0 0 0.694537 0 0 0 0 0.21775 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_159273_52037"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_159273_52037" result="shape"/>
</filter>
<linearGradient id="paint0_linear_159273_52037" x1="38.5" y1="15" x2="38.5" y2="50" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBE767"/>
<stop offset="1" stop-color="#E59218"/>
</linearGradient>
<linearGradient id="paint1_linear_159273_52037" x1="29" y1="2" x2="29" y2="42" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F6C986"/>
</linearGradient>
<linearGradient id="paint2_linear_159273_52037" x1="29" y1="9" x2="29" y2="32.6923" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FFEFD4"/>
</linearGradient>
</defs>
</svg>
