<svg width="56" height="52" viewBox="0 0 56 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="31.0625" cy="21.8125" r="17.0625" fill="url(#paint0_linear_159273_52069)"/>
<foreignObject x="7.5" y="0" width="45" height="45"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_159273_52069_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_dii_159273_52069)" data-figma-bg-blur-radius="3">
<circle cx="28" cy="20.5" r="17.5" fill="url(#paint1_linear_159273_52069)" fill-opacity="0.8" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter1_d_159273_52069)">
<path d="M26.6353 30.7308V28.0238H29.6049V30.7308H26.6353ZM26.6353 12.707V10H29.6049V12.707H26.6353ZM28.1419 28.8133C27.0137 28.8133 26.0056 28.599 25.1176 28.1704C24.2296 27.7343 23.5054 27.1215 22.945 26.332C22.3845 25.5349 22.0279 24.595 21.875 23.5122L24.8229 23.0385C25.0194 23.9258 25.4306 24.6326 26.0566 25.159C26.6898 25.6853 27.4432 25.9485 28.3166 25.9485C29.1391 25.9485 29.8197 25.7492 30.3583 25.3507C30.9042 24.9522 31.1771 24.4597 31.1771 23.8731C31.1771 23.4746 31.057 23.1475 30.8168 22.8919C30.5839 22.6287 30.2164 22.4144 29.7141 22.249L25.8273 21.0083C23.5855 20.294 22.4646 18.8803 22.4646 16.7674C22.4646 15.7748 22.6902 14.9139 23.1415 14.1845C23.6 13.4551 24.2442 12.8949 25.074 12.5039C25.911 12.1054 26.8973 11.9099 28.0328 11.9174C29.0736 11.9325 29.998 12.128 30.8059 12.5039C31.6139 12.8724 32.2871 13.41 32.8258 14.1168C33.3717 14.8161 33.7683 15.6696 34.0158 16.6772L30.9588 17.2411C30.8569 16.7599 30.6676 16.3388 30.391 15.9779C30.1145 15.6094 29.7687 15.3237 29.3538 15.1207C28.9462 14.9101 28.4913 14.7973 27.9891 14.7823C27.5087 14.7673 27.072 14.8387 26.6789 14.9966C26.2932 15.1545 25.9838 15.3763 25.7509 15.6621C25.5253 15.9403 25.4124 16.2561 25.4124 16.6095C25.4124 17.0005 25.5726 17.3389 25.8928 17.6246C26.2131 17.9028 26.7226 18.1434 27.4213 18.3464L30.1945 19.1585C31.5847 19.5721 32.5856 20.1323 33.197 20.8391C33.8157 21.5459 34.125 22.4821 34.125 23.6476C34.125 24.6702 33.8739 25.5688 33.3717 26.3432C32.8694 27.1177 32.167 27.723 31.2645 28.1592C30.3692 28.5953 29.3284 28.8133 28.1419 28.8133Z" fill="url(#paint2_linear_159273_52069)"/>
</g>
<path d="M45.2888 36.1398C44.9492 35.4364 44.2011 34.9689 43.4117 34.9629L36.5476 34.9175C36.5078 35.0748 36.4512 35.2337 36.3916 35.391C35.7261 37.1232 33.9378 38.1746 32.073 37.9915L31.8007 37.9643C29.2904 37.7192 26.8091 37.2578 24.3798 36.5861L22.44 36.0506C22.1325 35.9628 21.8755 35.7662 21.718 35.4939C21.5619 35.2155 21.5221 34.8948 21.6078 34.5922C21.6965 34.2851 21.8969 34.0325 22.1723 33.8797C22.4522 33.7254 22.7766 33.6845 23.084 33.7708L25.1278 34.3335C27.3399 34.9447 29.6024 35.3683 31.8879 35.5952L32.3147 35.6346C33.127 35.7163 33.8889 35.2473 34.1749 34.4924C34.3432 34.0476 34.3248 33.5635 34.1214 33.1308C33.9103 32.6967 33.5493 32.3714 33.101 32.2095L23.1483 28.6786C22.0606 28.2898 20.8521 28.2747 19.7507 28.6317L7.88877 32.9191C7.11929 33.1974 6.77509 34.0809 7.15601 34.798L10.8642 41.7751C11.1809 42.3712 11.8968 42.6404 12.5347 42.406L15.4137 41.3485C15.8727 41.1775 16.3806 41.1927 16.8303 41.3848L24.2023 44.5178C25.6143 45.1184 27.2053 45.1593 28.6463 44.6328L44.1246 38.993C44.6876 38.7858 45.1205 38.3713 45.3438 37.8206C45.5702 37.2699 45.5488 36.6739 45.2903 36.1414L45.2888 36.1398Z" fill="url(#paint3_linear_159273_52069)"/>
<foreignObject x="1.86719" y="27.4998" width="16.2559" height="20.1374"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_1_159273_52069_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter2_i_159273_52069)" data-figma-bg-blur-radius="4">
<path d="M11.9569 33.6067L13.944 39.3391C14.4765 40.8755 13.7865 42.5685 12.3317 43.2949C10.5434 44.1878 8.38127 43.2624 7.79269 41.3522L6.0062 35.5542C5.56655 34.1273 6.19554 32.5885 7.50881 31.8782C9.2073 30.9595 11.3244 31.7822 11.9569 33.6067Z" fill="url(#paint4_linear_159273_52069)" fill-opacity="0.5"/>
</g>
<circle cx="9.1875" cy="34.0625" r="1.3125" fill="white"/>
<defs>
<filter id="filter0_dii_159273_52069" x="7.5" y="0" width="45" height="45" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="3"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.396846 0 0 0 0 0.238369 0 0 0 0 0.785833 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_159273_52069"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_159273_52069" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_159273_52069"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_159273_52069" result="effect3_innerShadow_159273_52069"/>
</filter>
<clipPath id="bgblur_0_159273_52069_clip_path" transform="translate(-7.5 0)"><circle cx="28" cy="20.5" r="17.5"/>
</clipPath><filter id="filter1_d_159273_52069" x="17.875" y="8" width="20.25" height="28.7308" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.55737 0 0 0 0 0.376169 0 0 0 0 0.907692 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_159273_52069"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_159273_52069" result="shape"/>
</filter>
<filter id="filter2_i_159273_52069" x="1.86719" y="27.4998" width="16.2559" height="20.1374" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_159273_52069"/>
</filter>
<clipPath id="bgblur_1_159273_52069_clip_path" transform="translate(-1.86719 -27.4998)"><path d="M11.9569 33.6067L13.944 39.3391C14.4765 40.8755 13.7865 42.5685 12.3317 43.2949C10.5434 44.1878 8.38127 43.2624 7.79269 41.3522L6.0062 35.5542C5.56655 34.1273 6.19554 32.5885 7.50881 31.8782C9.2073 30.9595 11.3244 31.7822 11.9569 33.6067Z"/>
</clipPath><linearGradient id="paint0_linear_159273_52069" x1="31.0625" y1="4.75" x2="31.0625" y2="38.875" gradientUnits="userSpaceOnUse">
<stop stop-color="#8660E3"/>
<stop offset="1" stop-color="#592BCC"/>
</linearGradient>
<linearGradient id="paint1_linear_159273_52069" x1="28" y1="3" x2="28" y2="38" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#CFBCFF"/>
</linearGradient>
<linearGradient id="paint2_linear_159273_52069" x1="28" y1="10" x2="28" y2="30.7308" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#C3ABF4"/>
</linearGradient>
<linearGradient id="paint3_linear_159273_52069" x1="26.25" y1="28.375" x2="26.25" y2="45" gradientUnits="userSpaceOnUse">
<stop stop-color="#C0A8FC"/>
<stop offset="1" stop-color="#6C40D8"/>
</linearGradient>
<linearGradient id="paint4_linear_159273_52069" x1="10.0625" y1="30.125" x2="10.0625" y2="45" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#EDE7FF"/>
</linearGradient>
</defs>
</svg>
