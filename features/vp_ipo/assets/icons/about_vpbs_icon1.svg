<svg width="62" height="56" viewBox="0 0 62 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_159273_52016)">
<g filter="url(#filter0_d_159273_52016)">
<path d="M40.4144 28.1701C51.4812 29.9518 57.0536 18.9789 57.824 14.9468C58.1593 13.192 58.6283 11.3236 57.2867 11.0672C55.7071 10.7654 54.2419 13.1517 50.7321 12.4811L51.1415 10.3381C54.4306 10.9665 55.359 8.47764 57.6962 8.9242C60.0115 9.36658 60.5758 11.6957 59.8796 15.3395C58.0733 24.793 49.0698 32.0451 40.0049 30.3131L40.4144 28.1701Z" fill="url(#paint0_linear_159273_52016)"/>
<path d="M23.4124 24.9217C12.4683 22.4978 11.3338 10.2434 12.1042 6.2113C12.4394 4.45656 12.6923 2.54681 14.0339 2.80315C15.6135 3.10496 16.0958 5.86333 19.6056 6.53394L20.0151 4.3909C16.726 3.76247 16.7806 1.10668 14.4434 0.660114C12.128 0.217726 10.7448 2.17471 10.0486 5.81855C8.24238 15.272 13.938 25.3327 23.0029 27.0647L23.4124 24.9217Z" fill="url(#paint1_linear_159273_52016)"/>
<path d="M19.644 40.9332L19.1872 43.3244C18.9348 44.6453 22.8425 46.5014 27.9147 47.4705C32.9869 48.4397 37.3036 48.155 37.556 46.8341L38.0129 44.4428L19.644 40.9332Z" fill="url(#paint2_linear_159273_52016)"/>
<path d="M28.3717 45.0793C33.4441 46.0484 37.7607 45.7635 38.013 44.4428C38.2654 43.1222 34.3579 41.2659 29.2855 40.2967C24.2131 39.3276 19.8965 39.6125 19.6442 40.9332C19.3918 42.2538 23.2993 44.1101 28.3717 45.0793Z" fill="url(#paint3_linear_159273_52016)"/>
<path d="M33.0859 35.498C32.3001 39.611 34.0091 41.7422 34.9924 42.5957C35.3705 42.9241 35.552 43.4214 35.4586 43.9099C35.3031 44.7241 32.2013 44.8739 28.5443 44.1751C24.8874 43.4764 22.0595 42.1925 22.2149 41.3795C22.3084 40.8899 22.6604 40.4944 23.133 40.3286C24.3616 39.8977 26.7361 38.5467 27.5219 34.4338L33.0838 35.4965L33.0859 35.498Z" fill="url(#paint4_linear_159273_52016)"/>
<path d="M51.2205 14.3601L52.3256 8.57668L19.5645 2.31714L18.4594 8.1006C15.298 24.6471 21.1429 33.8392 30.1905 35.5679C39.237 37.2964 48.1677 30.3443 51.2217 14.3604L51.2205 14.3601Z" fill="url(#paint5_linear_159273_52016)"/>
<path d="M35.6219 7.13321C44.669 8.86179 52.1473 9.50805 52.3253 8.57667C52.5032 7.64529 45.3134 5.48896 36.2664 3.76037C27.2193 2.03179 19.741 1.38553 19.563 2.31691C19.3851 3.24829 26.5749 5.40462 35.6219 7.13321Z" fill="url(#paint6_linear_159273_52016)"/>
</g>
<foreignObject x="20" y="9" width="32" height="34"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_1_159273_52016_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_dii_159273_52016)" data-figma-bg-blur-radius="3">
<circle cx="33" cy="20" r="8" fill="url(#paint7_linear_159273_52016)" fill-opacity="0.5" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter2_d_159273_52016)">
<path d="M35.9022 16.047L35.5917 18.7554C35.5478 19.1401 35.3729 19.4968 35.0988 19.7605L32.7996 21.9728L33.2341 18.1827L35.6139 15.8943C35.6403 15.8698 35.673 15.8542 35.7078 15.8493C35.7426 15.8445 35.7779 15.8508 35.8091 15.8673C35.8403 15.8838 35.8658 15.9098 35.8824 15.9418C35.899 15.9739 35.9059 16.0105 35.9022 16.047Z" fill="white"/>
</g>
<g filter="url(#filter3_d_159273_52016)">
<path d="M30.7195 15.4554L30.4092 18.1625C30.3648 18.5468 30.4544 18.9337 30.6616 19.2524L32.401 21.9271L32.8353 18.1382L31.0364 15.3721C31.0166 15.3416 30.9882 15.3181 30.9551 15.305C30.922 15.292 30.8857 15.2898 30.8512 15.2989C30.8166 15.3079 30.7855 15.3278 30.762 15.3558C30.7384 15.3838 30.7236 15.4185 30.7195 15.4554Z" fill="white"/>
</g>
<g filter="url(#filter4_d_159273_52016)">
<path d="M38.5188 19.8012L37.1979 19.6498C36.8195 19.6064 36.4364 19.6435 36.0746 19.7586C35.7129 19.8737 35.381 20.064 35.1016 20.3167L32.745 22.4509L32.4342 25.162C32.8366 25.1553 33.2226 25.0068 33.5215 24.7436L38.6188 20.1155C38.6455 20.0926 38.6649 20.0626 38.6745 20.0291C38.6841 19.9956 38.6836 19.9601 38.673 19.9268C38.6624 19.8935 38.6422 19.8639 38.6148 19.8416C38.5874 19.8193 38.5541 19.8053 38.5188 19.8012Z" fill="url(#paint8_linear_159273_52016)"/>
</g>
<g filter="url(#filter5_d_159273_52016)">
<path d="M27.3207 18.5176L28.643 18.6691C29.0211 18.7124 29.3856 18.8352 29.7117 19.0292C30.0378 19.2231 30.3178 19.4836 30.5325 19.7929L32.3465 22.4052L32.0357 25.1163C31.6454 25.0189 31.3032 24.7869 31.0718 24.4627L27.1534 18.8012C27.1326 18.773 27.1206 18.7395 27.1188 18.7048C27.1169 18.6701 27.1254 18.6357 27.1431 18.6057C27.1608 18.5757 27.187 18.5514 27.2185 18.5358C27.25 18.5201 27.2855 18.5138 27.3207 18.5176Z" fill="url(#paint9_linear_159273_52016)"/>
</g>
</g>
<defs>
<filter id="filter0_d_159273_52016" x="-1" y="-1" width="66.1826" height="61.2323" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_159273_52016"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_159273_52016" result="shape"/>
</filter>
<filter id="filter1_dii_159273_52016" x="20" y="9" width="32" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="7"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.973333 0 0 0 0 0.447733 0 0 0 0 0.573877 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_159273_52016"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_159273_52016" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_159273_52016"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_159273_52016" result="effect3_innerShadow_159273_52016"/>
</filter>
<clipPath id="bgblur_1_159273_52016_clip_path" transform="translate(-20 -9)"><circle cx="33" cy="20" r="8"/>
</clipPath><filter id="filter2_d_159273_52016" x="28.7998" y="15.8478" width="11.1035" height="14.125" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.928333 0 0 0 0 0.4456 0 0 0 0 0.563477 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_159273_52016"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_159273_52016" result="shape"/>
</filter>
<filter id="filter3_d_159273_52016" x="26.3979" y="15.2933" width="10.4375" height="14.6338" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.928333 0 0 0 0 0.4456 0 0 0 0 0.563477 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_159273_52016"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_159273_52016" result="shape"/>
</filter>
<filter id="filter4_d_159273_52016" x="28.4341" y="19.6322" width="14.2471" height="13.5298" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.928333 0 0 0 0 0.4456 0 0 0 0 0.563477 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_159273_52016"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_159273_52016" result="shape"/>
</filter>
<filter id="filter5_d_159273_52016" x="23.1187" y="18.5165" width="13.228" height="14.5997" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.928333 0 0 0 0 0.4456 0 0 0 0 0.563477 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_159273_52016"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_159273_52016" result="shape"/>
</filter>
<linearGradient id="paint0_linear_159273_52016" x1="52.778" y1="7.98451" x2="48.2122" y2="31.8813" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF95AE"/>
<stop offset="1" stop-color="#FF6287"/>
</linearGradient>
<linearGradient id="paint1_linear_159273_52016" x1="19.3615" y1="1.5998" x2="14.7957" y2="25.4966" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF95AE"/>
<stop offset="1" stop-color="#FF6287"/>
</linearGradient>
<linearGradient id="paint2_linear_159273_52016" x1="19.1878" y1="43.3246" x2="37.556" y2="46.8341" gradientUnits="userSpaceOnUse">
<stop stop-color="#B01E41"/>
<stop offset="1" stop-color="#700E25"/>
</linearGradient>
<linearGradient id="paint3_linear_159273_52016" x1="19.6448" y1="40.9333" x2="38.013" y2="44.4428" gradientUnits="userSpaceOnUse">
<stop stop-color="#DF224F"/>
<stop offset="1" stop-color="#ED315F"/>
</linearGradient>
<linearGradient id="paint4_linear_159273_52016" x1="30.304" y1="34.9653" x2="28.5443" y2="44.1751" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF95AE"/>
<stop offset="1" stop-color="#FF6287"/>
</linearGradient>
<linearGradient id="paint5_linear_159273_52016" x1="35.9456" y1="5.44702" x2="30.1905" y2="35.5679" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF95AE"/>
<stop offset="1" stop-color="#FF6287"/>
</linearGradient>
<linearGradient id="paint6_linear_159273_52016" x1="36.2664" y1="3.76037" x2="35.6219" y2="7.13321" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF688B"/>
<stop offset="1" stop-color="#FC2154"/>
</linearGradient>
<linearGradient id="paint7_linear_159273_52016" x1="33" y1="12" x2="33" y2="28" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FFB9CB"/>
</linearGradient>
<linearGradient id="paint8_linear_159273_52016" x1="35.9101" y1="19.5022" x2="35.2247" y2="25.4818" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FFB7C8"/>
</linearGradient>
<linearGradient id="paint9_linear_159273_52016" x1="29.9306" y1="18.8167" x2="29.2452" y2="24.7964" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FFB7C8"/>
</linearGradient>
<clipPath id="clip0_159273_52016">
<rect width="64" height="64" fill="white" transform="translate(0 -8)"/>
</clipPath>
</defs>
</svg>
